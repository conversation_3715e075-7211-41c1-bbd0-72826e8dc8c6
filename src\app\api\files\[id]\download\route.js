import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import File from '@/models/File';
import User from '@/models/User';
import { optionalAuth } from '@/middleware/auth';

export async function GET(request, { params }) {
  try {
    const { id } = params;

    await connectDB();

    // Check if user is authenticated (optional)
    const user = await optionalAuth(request);

    // Find file
    const file = await File.findById(id);

    if (!file) {
      return NextResponse.json(
        { success: false, message: 'File not found' },
        { status: 404 }
      );
    }

    // Check if file is accessible
    if (file.status !== 'approved' || !file.isPublic) {
      // Only allow access to owner or admin
      if (!user || (user._id.toString() !== file.uploadedBy.toString() && user.role !== 'admin')) {
        return NextResponse.json(
          { success: false, message: 'File not accessible' },
          { status: 403 }
        );
      }
    }

    // Increment download count
    await File.findByIdAndUpdate(id, { $inc: { downloadCount: 1 } });

    // Update user download count if authenticated
    if (user) {
      await User.findByIdAndUpdate(user._id, { $inc: { downloadCount: 1 } });
    }

    // Return file URL for download
    return NextResponse.json({
      success: true,
      data: {
        downloadUrl: file.fileUrl,
        fileName: file.originalName,
        fileSize: file.fileSize
      }
    });

  } catch (error) {
    console.error('File download error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process download' },
      { status: 500 }
    );
  }
}

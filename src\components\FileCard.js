'use client';

import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { 
  EyeIcon, 
  ArrowDownTrayIcon, 
  StarIcon,
  DocumentIcon,
  PhotoIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

export default function FileCard({ file }) {
  const getFileIcon = (fileType) => {
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif'];
    if (imageTypes.includes(fileType.toLowerCase())) {
      return <PhotoIcon className="h-8 w-8 text-blue-500" />;
    }
    return <DocumentIcon className="h-8 w-8 text-gray-500" />;
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <StarIconSolid key={i} className="h-4 w-4 text-yellow-400" />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className="relative">
            <StarIcon className="h-4 w-4 text-gray-300" />
            <StarIconSolid className="h-4 w-4 text-yellow-400 absolute top-0 left-0 w-1/2 overflow-hidden" />
          </div>
        );
      } else {
        stars.push(
          <StarIcon key={i} className="h-4 w-4 text-gray-300" />
        );
      }
    }

    return stars;
  };

  return (
    <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden">
      {/* File preview/thumbnail */}
      <div className="h-48 bg-gray-50 flex items-center justify-center">
        {file.thumbnailUrl ? (
          <img
            src={file.thumbnailUrl}
            alt={file.title}
            className="h-full w-full object-cover"
          />
        ) : (
          <div className="flex flex-col items-center justify-center text-gray-400">
            {getFileIcon(file.fileType)}
            <span className="mt-2 text-sm font-medium uppercase">
              {file.fileType}
            </span>
          </div>
        )}
      </div>

      {/* File information */}
      <div className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <Link href={`/files/${file._id}`}>
              <h3 className="text-lg font-semibold text-gray-900 hover:text-blue-600 cursor-pointer truncate">
                {file.title}
              </h3>
            </Link>
            <p className="text-sm text-gray-600 mt-1 line-clamp-2">
              {file.description}
            </p>
          </div>
        </div>

        {/* File metadata */}
        <div className="mt-3 space-y-2">
          <div className="flex items-center justify-between text-sm text-gray-500">
            <span className="font-medium">{file.subject}</span>
            <span>{formatFileSize(file.fileSize)}</span>
          </div>
          
          <div className="flex items-center justify-between text-sm text-gray-500">
            <span>{file.course}</span>
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
              {file.category?.name}
            </span>
          </div>

          {/* Author and upload date */}
          <div className="flex items-center justify-between text-sm text-gray-500">
            <span>by {file.uploadedBy?.name}</span>
            <span>{formatDistanceToNow(new Date(file.createdAt), { addSuffix: true })}</span>
          </div>
        </div>

        {/* Rating */}
        {file.rating && file.rating.count > 0 && (
          <div className="mt-3 flex items-center">
            <div className="flex items-center">
              {renderStars(file.rating.average)}
            </div>
            <span className="ml-2 text-sm text-gray-600">
              {file.rating.average.toFixed(1)} ({file.rating.count} reviews)
            </span>
          </div>
        )}

        {/* Tags */}
        {file.tags && file.tags.length > 0 && (
          <div className="mt-3">
            <div className="flex flex-wrap gap-1">
              {file.tags.slice(0, 3).map((tag, index) => (
                <span
                  key={index}
                  className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded"
                >
                  {tag}
                </span>
              ))}
              {file.tags.length > 3 && (
                <span className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                  +{file.tags.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        {/* Stats and actions */}
        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <div className="flex items-center">
              <EyeIcon className="h-4 w-4 mr-1" />
              {file.viewCount || 0}
            </div>
            <div className="flex items-center">
              <ArrowDownTrayIcon className="h-4 w-4 mr-1" />
              {file.downloadCount || 0}
            </div>
          </div>

          <Link
            href={`/files/${file._id}`}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
          >
            View Details
          </Link>
        </div>
      </div>
    </div>
  );
}

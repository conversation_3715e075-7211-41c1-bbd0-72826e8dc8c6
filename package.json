{"name": "notex", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "seed": "node scripts/seed.js"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "date-fns": "^4.1.0", "imagekit": "^6.0.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.536.0", "mongoose": "^8.17.0", "multer": "^2.0.2", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "react-icons": "^5.5.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4"}}
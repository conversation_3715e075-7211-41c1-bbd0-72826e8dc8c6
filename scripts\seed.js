import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Import models
import User from '../src/models/User.js';
import Category from '../src/models/Category.js';

const MONGODB_URI = process.env.MONGODB_URI;

if (!MONGODB_URI) {
  console.error('Please define the MONGODB_URI environment variable inside .env.local');
  process.exit(1);
}

async function connectDB() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
}

async function seedDatabase() {
  try {
    console.log('Starting database seeding...');

    // Clear existing data
    await User.deleteMany({});
    await Category.deleteMany({});
    console.log('Cleared existing data');

    // Create admin user
    const adminPassword = await bcrypt.hash('admin123', 12);
    const adminUser = await User.create({
      name: 'Admin User',
      email: '<EMAIL>',
      password: adminPassword,
      role: 'admin',
      isActive: true,
      isVerified: true
    });
    console.log('Created admin user');

    // Create sample regular users
    const userPassword = await bcrypt.hash('password123', 12);
    const users = await User.create([
      {
        name: 'John Doe',
        email: '<EMAIL>',
        password: userPassword,
        role: 'user',
        institution: 'University of Technology',
        isActive: true,
        isVerified: true
      },
      {
        name: 'Jane Smith',
        email: '<EMAIL>',
        password: userPassword,
        role: 'user',
        institution: 'State University',
        isActive: true,
        isVerified: true
      },
      {
        name: 'Mike Johnson',
        email: '<EMAIL>',
        password: userPassword,
        role: 'user',
        institution: 'Community College',
        isActive: true,
        isVerified: true
      }
    ]);
    console.log('Created sample users');

    // Create categories
    const categories = await Category.create([
      {
        name: 'Computer Science',
        description: 'Programming, algorithms, data structures, and software engineering',
        icon: 'computer',
        color: '#3B82F6',
        createdBy: adminUser._id,
        order: 1
      },
      {
        name: 'Mathematics',
        description: 'Calculus, algebra, statistics, and mathematical analysis',
        icon: 'calculator',
        color: '#10B981',
        createdBy: adminUser._id,
        order: 2
      },
      {
        name: 'Physics',
        description: 'Classical mechanics, quantum physics, and thermodynamics',
        icon: 'atom',
        color: '#8B5CF6',
        createdBy: adminUser._id,
        order: 3
      },
      {
        name: 'Chemistry',
        description: 'Organic, inorganic, and physical chemistry',
        icon: 'beaker',
        color: '#F59E0B',
        createdBy: adminUser._id,
        order: 4
      },
      {
        name: 'Biology',
        description: 'Cell biology, genetics, ecology, and molecular biology',
        icon: 'leaf',
        color: '#059669',
        createdBy: adminUser._id,
        order: 5
      },
      {
        name: 'Engineering',
        description: 'Mechanical, electrical, civil, and chemical engineering',
        icon: 'cog',
        color: '#DC2626',
        createdBy: adminUser._id,
        order: 6
      },
      {
        name: 'Business',
        description: 'Management, finance, marketing, and entrepreneurship',
        icon: 'briefcase',
        color: '#7C3AED',
        createdBy: adminUser._id,
        order: 7
      },
      {
        name: 'Literature',
        description: 'English literature, creative writing, and literary analysis',
        icon: 'book',
        color: '#BE185D',
        createdBy: adminUser._id,
        order: 8
      },
      {
        name: 'History',
        description: 'World history, ancient civilizations, and historical analysis',
        icon: 'clock',
        color: '#92400E',
        createdBy: adminUser._id,
        order: 9
      },
      {
        name: 'Art & Design',
        description: 'Visual arts, graphic design, and creative studies',
        icon: 'palette',
        color: '#EC4899',
        createdBy: adminUser._id,
        order: 10
      }
    ]);
    console.log('Created categories');

    console.log('Database seeding completed successfully!');
    console.log('\nCreated accounts:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('User 1: <EMAIL> / password123');
    console.log('User 2: <EMAIL> / password123');
    console.log('User 3: <EMAIL> / password123');

  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
}

async function main() {
  await connectDB();
  await seedDatabase();
  await mongoose.connection.close();
  console.log('\nDatabase connection closed');
  process.exit(0);
}

main();

import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import File from '@/models/File';
import { optionalAuth } from '@/middleware/auth';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 12;
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || '';
    const subject = searchParams.get('subject') || '';
    const fileType = searchParams.get('fileType') || '';
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    await connectDB();

    // Check if user is authenticated (optional)
    const user = await optionalAuth(request);

    // Build query
    const query = {
      status: 'approved',
      isPublic: true
    };

    // Add search functionality
    if (search) {
      query.$text = { $search: search };
    }

    // Add filters
    if (category) {
      query.category = category;
    }

    if (subject) {
      query.subject = { $regex: subject, $options: 'i' };
    }

    if (fileType) {
      query.fileType = fileType;
    }

    // Build sort object
    const sort = {};
    if (search && !sortBy) {
      sort.score = { $meta: 'textScore' };
    } else {
      sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
    }

    // Calculate skip value
    const skip = (page - 1) * limit;

    // Execute query with pagination
    const [files, totalFiles] = await Promise.all([
      File.find(query)
        .populate('uploadedBy', 'name')
        .populate('category', 'name slug')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      File.countDocuments(query)
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalFiles / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      data: {
        files,
        pagination: {
          currentPage: page,
          totalPages,
          totalFiles,
          hasNextPage,
          hasPrevPage,
          limit
        }
      }
    });

  } catch (error) {
    console.error('Files listing error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch files' },
      { status: 500 }
    );
  }
}

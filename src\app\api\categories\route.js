import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Category from '@/models/Category';
import { requireAdmin } from '@/middleware/auth';

export async function GET(request) {
  try {
    await connectDB();

    const categories = await Category.find({ isActive: true })
      .sort({ order: 1, name: 1 })
      .lean();

    return NextResponse.json({
      success: true,
      data: { categories }
    });

  } catch (error) {
    console.error('Categories listing error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}

async function createCategoryHandler(request) {
  try {
    const { name, description, icon, color } = await request.json();

    if (!name) {
      return NextResponse.json(
        { success: false, message: 'Category name is required' },
        { status: 400 }
      );
    }

    await connectDB();

    // Check if category already exists
    const existingCategory = await Category.findOne({ name });
    if (existingCategory) {
      return NextResponse.json(
        { success: false, message: 'Category with this name already exists' },
        { status: 400 }
      );
    }

    const category = await Category.create({
      name,
      description: description || '',
      icon: icon || 'folder',
      color: color || '#3B82F6',
      createdBy: request.user._id
    });

    return NextResponse.json({
      success: true,
      message: 'Category created successfully',
      data: { category }
    }, { status: 201 });

  } catch (error) {
    console.error('Category creation error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create category' },
      { status: 500 }
    );
  }
}

export const POST = requireAdmin(createCategoryHandler);

const MAX_FILE_SIZE = parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE) || 10485760; // 10MB
const ALLOWED_FILE_TYPES = process.env.NEXT_PUBLIC_ALLOWED_FILE_TYPES?.split(',') || 
  ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'txt', 'jpg', 'jpeg', 'png', 'gif'];

export const validateFile = (file) => {
  const errors = [];

  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    errors.push(`File size must be less than ${MAX_FILE_SIZE / 1024 / 1024}MB`);
  }

  // Check file type
  const fileExtension = file.name.split('.').pop().toLowerCase();
  if (!ALLOWED_FILE_TYPES.includes(fileExtension)) {
    errors.push(`File type .${fileExtension} is not allowed. Allowed types: ${ALLOWED_FILE_TYPES.join(', ')}`);
  }

  // Check file name
  if (!file.name || file.name.trim().length === 0) {
    errors.push('File name is required');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const getFileType = (filename) => {
  return filename.split('.').pop().toLowerCase();
};

export const generateFileName = (originalName) => {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const extension = getFileType(originalName);
  return `${timestamp}_${randomString}.${extension}`;
};

export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const isImageFile = (fileType) => {
  return ['jpg', 'jpeg', 'png', 'gif'].includes(fileType.toLowerCase());
};

export const isDocumentFile = (fileType) => {
  return ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'txt'].includes(fileType.toLowerCase());
};

export const getFileIcon = (fileType) => {
  const iconMap = {
    pdf: '📄',
    doc: '📝',
    docx: '📝',
    ppt: '📊',
    pptx: '📊',
    txt: '📄',
    jpg: '🖼️',
    jpeg: '🖼️',
    png: '🖼️',
    gif: '🖼️'
  };
  
  return iconMap[fileType.toLowerCase()] || '📁';
};

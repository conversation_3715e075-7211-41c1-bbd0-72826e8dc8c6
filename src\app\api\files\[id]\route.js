import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import File from '@/models/File';
import Comment from '@/models/Comment';
import Rating from '@/models/Rating';
import { optionalAuth } from '@/middleware/auth';

export async function GET(request, { params }) {
  try {
    const { id } = params;

    await connectDB();

    // Check if user is authenticated (optional)
    const user = await optionalAuth(request);

    // Find file
    const file = await File.findById(id)
      .populate('uploadedBy', 'name email avatar institution')
      .populate('category', 'name slug color icon');

    if (!file) {
      return NextResponse.json(
        { success: false, message: 'File not found' },
        { status: 404 }
      );
    }

    // Check if file is accessible
    if (file.status !== 'approved' || !file.isPublic) {
      // Only allow access to owner or admin
      if (!user || (user._id.toString() !== file.uploadedBy._id.toString() && user.role !== 'admin')) {
        return NextResponse.json(
          { success: false, message: 'File not accessible' },
          { status: 403 }
        );
      }
    }

    // Increment view count
    await File.findByIdAndUpdate(id, { $inc: { viewCount: 1 } });

    // Get comments for this file
    const comments = await Comment.find({ 
      file: id, 
      isApproved: true,
      parentComment: null 
    })
      .populate('author', 'name avatar')
      .populate({
        path: 'replies',
        populate: {
          path: 'author',
          select: 'name avatar'
        }
      })
      .sort({ createdAt: -1 })
      .limit(10);

    // Get user's rating if authenticated
    let userRating = null;
    if (user) {
      userRating = await Rating.findOne({ user: user._id, file: id });
    }

    // Get recent ratings
    const recentRatings = await Rating.find({ 
      file: id, 
      isApproved: true 
    })
      .populate('user', 'name avatar')
      .sort({ createdAt: -1 })
      .limit(5);

    return NextResponse.json({
      success: true,
      data: {
        file: {
          ...file.toObject(),
          viewCount: file.viewCount + 1
        },
        comments,
        userRating,
        recentRatings
      }
    });

  } catch (error) {
    console.error('File details error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch file details' },
      { status: 500 }
    );
  }
}

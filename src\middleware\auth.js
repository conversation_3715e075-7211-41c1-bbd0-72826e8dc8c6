import { verifyToken } from '@/lib/jwt';
import User from '@/models/User';
import connectDB from '@/lib/mongodb';

export const authenticate = async (req) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new Error('No token provided');
    }

    const token = authHeader.substring(7);
    const decoded = verifyToken(token);

    await connectDB();
    const user = await User.findById(decoded.userId).select('-password');
    
    if (!user || !user.isActive) {
      throw new Error('User not found or inactive');
    }

    return user;
  } catch (error) {
    throw new Error('Authentication failed');
  }
};

export const requireAuth = (handler) => {
  return async (req, res) => {
    try {
      const user = await authenticate(req);
      req.user = user;
      return handler(req, res);
    } catch (error) {
      return res.status(401).json({
        success: false,
        message: error.message || 'Authentication required'
      });
    }
  };
};

export const requireAdmin = (handler) => {
  return async (req, res) => {
    try {
      const user = await authenticate(req);
      
      if (user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Admin access required'
        });
      }

      req.user = user;
      return handler(req, res);
    } catch (error) {
      return res.status(401).json({
        success: false,
        message: error.message || 'Authentication required'
      });
    }
  };
};

export const optionalAuth = async (req) => {
  try {
    const user = await authenticate(req);
    return user;
  } catch (error) {
    return null;
  }
};

[{"name":"hot-reloader","duration":93,"timestamp":10489442272,"id":3,"tags":{"version":"15.4.5"},"startTime":1754235101204,"traceId":"0564f8f942be8262"},{"name":"setup-dev-bundler","duration":9274495,"timestamp":10480649330,"id":2,"parentId":1,"tags":{},"startTime":1754235092411,"traceId":"0564f8f942be8262"},{"name":"start-dev-server","duration":11881057,"timestamp":10479550892,"id":1,"tags":{"cpus":"4","platform":"win32","memory.freeMem":"782946304","memory.totalMem":"8299257856","memory.heapSizeLimit":"4199546880","memory.rss":"121561088","memory.heapTotal":"51716096","memory.heapUsed":"48319240"},"startTime":1754235091313,"traceId":"0564f8f942be8262"},{"name":"compile-path","duration":6180945,"timestamp":10491440040,"id":6,"tags":{"trigger":"/"},"startTime":1754235103202,"traceId":"0564f8f942be8262"},{"name":"ensure-page","duration":6184638,"timestamp":10491439684,"id":5,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1754235103202,"traceId":"0564f8f942be8262"}]
[{"name":"ensure-page","duration":37452,"timestamp":10497631502,"id":7,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1754235109393,"traceId":"0564f8f942be8262"},{"name":"handle-request","duration":7062515,"timestamp":10491433639,"id":4,"tags":{"url":"/"},"startTime":1754235103196,"traceId":"0564f8f942be8262"},{"name":"memory-usage","duration":13,"timestamp":10498496442,"id":8,"parentId":4,"tags":{"url":"/","memory.rss":"475938816","memory.heapUsed":"75060880","memory.heapTotal":"110747648"},"startTime":1754235110258,"traceId":"0564f8f942be8262"},{"name":"compile-path","duration":144331,"timestamp":10619286785,"id":11,"tags":{"trigger":"/"},"startTime":1754235231043,"traceId":"0564f8f942be8262"}]
[{"name":"ensure-page","duration":51593,"timestamp":10619439862,"id":12,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1754235231196,"traceId":"0564f8f942be8262"},{"name":"client-hmr-latency","duration":748000,"timestamp":10618681416,"id":13,"parentId":3,"tags":{"updatedModules":["[project]/src/contexts/AuthContext.js","[project]/src/components/Navigation.js","[project]/src/components/FileCard.js","[project]/src/components/Loading.js","[project]/src/app/page.js","[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js","[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js","[project]/node_modules/next/dist/shared/lib/router/utils/querystring.js","[project]/node_modules/next/dist/shared/lib/router/utils/format-url.js","[project]/node_modules/next/dist/shared/lib/utils.js","[project]/node_modules/next/dist/shared/lib/router/utils/is-local-url.js","[project]/node_modules/next/dist/shared/lib/utils/error-once.js","[project]/node_modules/next/dist/client/app-dir/link.js","[project]/node_modules/@heroicons/react/24/outline/esm/BookOpenIcon.js","[project]/node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js","[project]/node_modules/@heroicons/react/24/outline/esm/CloudArrowUpIcon.js","[project]/node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js","[project]/node_modules/@heroicons/react/24/outline/esm/StarIcon.js","[project]/node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js","[project]/node_modules/@heroicons/react/24/outline/esm/UserIcon.js","[project]/node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js","[project]/node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js","[project]/node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js","[project]/node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js","[project]/node_modules/@heroicons/react/24/outline/esm/PlusIcon.js","[project]/node_modules/date-fns/constants.js","[project]/node_modules/date-fns/constructFrom.js","[project]/node_modules/date-fns/constructNow.js","[project]/node_modules/date-fns/locale/en-US/_lib/formatDistance.js","[project]/node_modules/date-fns/locale/_lib/buildFormatLongFn.js","[project]/node_modules/date-fns/locale/en-US/_lib/formatLong.js","[project]/node_modules/date-fns/locale/en-US/_lib/formatRelative.js","[project]/node_modules/date-fns/locale/_lib/buildLocalizeFn.js","[project]/node_modules/date-fns/locale/en-US/_lib/localize.js","[project]/node_modules/date-fns/locale/_lib/buildMatchFn.js","[project]/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js","[project]/node_modules/date-fns/locale/en-US/_lib/match.js","[project]/node_modules/date-fns/locale/en-US.js","[project]/node_modules/date-fns/_lib/defaultOptions.js","[project]/node_modules/date-fns/toDate.js","[project]/node_modules/date-fns/_lib/getTimezoneOffsetInMilliseconds.js","[project]/node_modules/date-fns/_lib/normalizeDates.js","[project]/node_modules/date-fns/compareAsc.js","[project]/node_modules/date-fns/differenceInCalendarMonths.js","[project]/node_modules/date-fns/endOfDay.js","[project]/node_modules/date-fns/endOfMonth.js","[project]/node_modules/date-fns/isLastDayOfMonth.js","[project]/node_modules/date-fns/differenceInMonths.js","[project]/node_modules/date-fns/_lib/getRoundingMethod.js","[project]/node_modules/date-fns/differenceInMilliseconds.js","[project]/node_modules/date-fns/differenceInSeconds.js","[project]/node_modules/date-fns/formatDistance.js","[project]/node_modules/date-fns/formatDistanceToNow.js","[project]/node_modules/@heroicons/react/24/outline/esm/EyeIcon.js","[project]/node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js","[project]/node_modules/@heroicons/react/24/outline/esm/DocumentIcon.js","[project]/node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js","[project]/node_modules/@heroicons/react/24/solid/esm/StarIcon.js"],"page":"/","isPageHidden":true},"startTime":1754235231583,"traceId":"0564f8f942be8262"},{"name":"handle-request","duration":548949,"timestamp":10619281406,"id":9,"tags":{"url":"/?_rsc=fsnpp"},"startTime":1754235231038,"traceId":"0564f8f942be8262"},{"name":"memory-usage","duration":7,"timestamp":10619830464,"id":14,"parentId":9,"tags":{"url":"/?_rsc=fsnpp","memory.rss":"540516352","memory.heapUsed":"90075960","memory.heapTotal":"109588480"},"startTime":1754235231587,"traceId":"0564f8f942be8262"},{"name":"compile-path","duration":1020242,"timestamp":10620035717,"id":17,"tags":{"trigger":"/api/files"},"startTime":1754235231792,"traceId":"0564f8f942be8262"}]

{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/lib/mongodb.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAEA;;;;CAIC,GACD,IAAI,SAAS,4CAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,4CAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/models/File.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst FileSchema = new mongoose.Schema({\n  title: {\n    type: String,\n    required: [true, 'Title is required'],\n    trim: true,\n    maxlength: [200, 'Title cannot be more than 200 characters']\n  },\n  description: {\n    type: String,\n    required: [true, 'Description is required'],\n    trim: true,\n    maxlength: [1000, 'Description cannot be more than 1000 characters']\n  },\n  fileName: {\n    type: String,\n    required: [true, 'File name is required']\n  },\n  originalName: {\n    type: String,\n    required: [true, 'Original file name is required']\n  },\n  fileType: {\n    type: String,\n    required: [true, 'File type is required'],\n    enum: ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'txt', 'jpg', 'jpeg', 'png', 'gif']\n  },\n  fileSize: {\n    type: Number,\n    required: [true, 'File size is required']\n  },\n  fileUrl: {\n    type: String,\n    required: [true, 'File URL is required']\n  },\n  thumbnailUrl: {\n    type: String,\n    default: null\n  },\n  uploadedBy: {\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'User',\n    required: [true, 'Uploader is required']\n  },\n  subject: {\n    type: String,\n    required: [true, 'Subject is required'],\n    trim: true,\n    maxlength: [100, 'Subject cannot be more than 100 characters']\n  },\n  course: {\n    type: String,\n    required: [true, 'Course is required'],\n    trim: true,\n    maxlength: [100, 'Course cannot be more than 100 characters']\n  },\n  category: {\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'Category',\n    required: [true, 'Category is required']\n  },\n  tags: [{\n    type: String,\n    trim: true,\n    maxlength: [30, 'Tag cannot be more than 30 characters']\n  }],\n  status: {\n    type: String,\n    enum: ['pending', 'approved', 'rejected'],\n    default: 'pending'\n  },\n  isPublic: {\n    type: Boolean,\n    default: true\n  },\n  downloadCount: {\n    type: Number,\n    default: 0\n  },\n  viewCount: {\n    type: Number,\n    default: 0\n  },\n  rating: {\n    average: {\n      type: Number,\n      default: 0,\n      min: 0,\n      max: 5\n    },\n    count: {\n      type: Number,\n      default: 0\n    }\n  },\n  moderationNotes: {\n    type: String,\n    default: ''\n  },\n  approvedBy: {\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'User',\n    default: null\n  },\n  approvedAt: {\n    type: Date,\n    default: null\n  }\n}, {\n  timestamps: true\n});\n\n// Indexes for better query performance\nFileSchema.index({ uploadedBy: 1 });\nFileSchema.index({ category: 1 });\nFileSchema.index({ subject: 1 });\nFileSchema.index({ course: 1 });\nFileSchema.index({ status: 1 });\nFileSchema.index({ isPublic: 1 });\nFileSchema.index({ createdAt: -1 });\nFileSchema.index({ downloadCount: -1 });\nFileSchema.index({ 'rating.average': -1 });\n\n// Text index for search functionality\nFileSchema.index({\n  title: 'text',\n  description: 'text',\n  subject: 'text',\n  course: 'text',\n  tags: 'text'\n});\n\n// Virtual for comments\nFileSchema.virtual('comments', {\n  ref: 'Comment',\n  localField: '_id',\n  foreignField: 'file'\n});\n\n// Ensure virtual fields are serialized\nFileSchema.set('toJSON', { virtuals: true });\n\nexport default mongoose.models.File || mongoose.model('File', FileSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IACrC,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,MAAM;QACN,WAAW;YAAC;YAAK;SAA2C;IAC9D;IACA,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA0B;QAC3C,MAAM;QACN,WAAW;YAAC;YAAM;SAAkD;IACtE;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAwB;IAC3C;IACA,cAAc;QACZ,MAAM;QACN,UAAU;YAAC;YAAM;SAAiC;IACpD;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAwB;QACzC,MAAM;YAAC;YAAO;YAAO;YAAQ;YAAO;YAAQ;YAAO;YAAO;YAAQ;YAAO;SAAM;IACjF;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAwB;IAC3C;IACA,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;IAC1C;IACA,cAAc;QACZ,MAAM;QACN,SAAS;IACX;IACA,YAAY;QACV,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,KAAK;QACL,UAAU;YAAC;YAAM;SAAuB;IAC1C;IACA,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAAsB;QACvC,MAAM;QACN,WAAW;YAAC;YAAK;SAA6C;IAChE;IACA,QAAQ;QACN,MAAM;QACN,UAAU;YAAC;YAAM;SAAqB;QACtC,MAAM;QACN,WAAW;YAAC;YAAK;SAA4C;IAC/D;IACA,UAAU;QACR,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,KAAK;QACL,UAAU;YAAC;YAAM;SAAuB;IAC1C;IACA,MAAM;QAAC;YACL,MAAM;YACN,MAAM;YACN,WAAW;gBAAC;gBAAI;aAAwC;QAC1D;KAAE;IACF,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAW;YAAY;SAAW;QACzC,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,eAAe;QACb,MAAM;QACN,SAAS;IACX;IACA,WAAW;QACT,MAAM;QACN,SAAS;IACX;IACA,QAAQ;QACN,SAAS;YACP,MAAM;YACN,SAAS;YACT,KAAK;YACL,KAAK;QACP;QACA,OAAO;YACL,MAAM;YACN,SAAS;QACX;IACF;IACA,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,YAAY;QACV,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,KAAK;QACL,SAAS;IACX;IACA,YAAY;QACV,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;AACd;AAEA,uCAAuC;AACvC,WAAW,KAAK,CAAC;IAAE,YAAY;AAAE;AACjC,WAAW,KAAK,CAAC;IAAE,UAAU;AAAE;AAC/B,WAAW,KAAK,CAAC;IAAE,SAAS;AAAE;AAC9B,WAAW,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC7B,WAAW,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC7B,WAAW,KAAK,CAAC;IAAE,UAAU;AAAE;AAC/B,WAAW,KAAK,CAAC;IAAE,WAAW,CAAC;AAAE;AACjC,WAAW,KAAK,CAAC;IAAE,eAAe,CAAC;AAAE;AACrC,WAAW,KAAK,CAAC;IAAE,kBAAkB,CAAC;AAAE;AAExC,sCAAsC;AACtC,WAAW,KAAK,CAAC;IACf,OAAO;IACP,aAAa;IACb,SAAS;IACT,QAAQ;IACR,MAAM;AACR;AAEA,uBAAuB;AACvB,WAAW,OAAO,CAAC,YAAY;IAC7B,KAAK;IACL,YAAY;IACZ,cAAc;AAChB;AAEA,uCAAuC;AACvC,WAAW,GAAG,CAAC,UAAU;IAAE,UAAU;AAAK;uCAE3B,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ", "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/lib/jwt.js"], "sourcesContent": ["import jwt from 'jsonwebtoken';\n\nconst JWT_SECRET = process.env.JWT_SECRET;\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';\n\nif (!JWT_SECRET) {\n  throw new Error('Please define the JWT_SECRET environment variable inside .env.local');\n}\n\nexport const signToken = (payload) => {\n  return jwt.sign(payload, JWT_SECRET, {\n    expiresIn: JWT_EXPIRES_IN,\n  });\n};\n\nexport const verifyToken = (token) => {\n  try {\n    return jwt.verify(token, JWT_SECRET);\n  } catch (error) {\n    throw new Error('Invalid token');\n  }\n};\n\nexport const generateTokens = (user) => {\n  const payload = {\n    userId: user._id,\n    email: user.email,\n    role: user.role,\n  };\n\n  const accessToken = signToken(payload);\n  \n  return {\n    accessToken,\n    user: {\n      id: user._id,\n      name: user.name,\n      email: user.email,\n      role: user.role,\n      avatar: user.avatar,\n      isVerified: user.isVerified,\n    },\n  };\n};\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU;AACzC,MAAM,iBAAiB,QAAQ,GAAG,CAAC,cAAc,IAAI;AAErD,IAAI,CAAC,YAAY;IACf,MAAM,IAAI,MAAM;AAClB;AAEO,MAAM,YAAY,CAAC;IACxB,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QACnC,WAAW;IACb;AACF;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,UAAU;QACd,QAAQ,KAAK,GAAG;QAChB,OAAO,KAAK,KAAK;QACjB,MAAM,KAAK,IAAI;IACjB;IAEA,MAAM,cAAc,UAAU;IAE9B,OAAO;QACL;QACA,MAAM;YACJ,IAAI,KAAK,GAAG;YACZ,MAAM,KAAK,IAAI;YACf,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;YACf,QAAQ,KAAK,MAAM;YACnB,YAAY,KAAK,UAAU;QAC7B;IACF;AACF", "debugId": null}}, {"offset": {"line": 429, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/models/User.js"], "sourcesContent": ["import mongoose from 'mongoose';\nimport bcrypt from 'bcryptjs';\n\nconst UserSchema = new mongoose.Schema({\n  name: {\n    type: String,\n    required: [true, 'Name is required'],\n    trim: true,\n    maxlength: [50, 'Name cannot be more than 50 characters']\n  },\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    unique: true,\n    lowercase: true,\n    trim: true,\n    match: [\n      /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/,\n      'Please enter a valid email'\n    ]\n  },\n  password: {\n    type: String,\n    required: [true, 'Password is required'],\n    minlength: [6, 'Password must be at least 6 characters'],\n    select: false\n  },\n  role: {\n    type: String,\n    enum: ['user', 'admin'],\n    default: 'user'\n  },\n  avatar: {\n    type: String,\n    default: null\n  },\n  bio: {\n    type: String,\n    maxlength: [500, 'Bio cannot be more than 500 characters'],\n    default: ''\n  },\n  institution: {\n    type: String,\n    maxlength: [100, 'Institution name cannot be more than 100 characters'],\n    default: ''\n  },\n  isActive: {\n    type: Boolean,\n    default: true\n  },\n  isVerified: {\n    type: Boolean,\n    default: false\n  },\n  uploadCount: {\n    type: Number,\n    default: 0\n  },\n  downloadCount: {\n    type: Number,\n    default: 0\n  },\n  lastLogin: {\n    type: Date,\n    default: null\n  }\n}, {\n  timestamps: true\n});\n\n// Index for better query performance\nUserSchema.index({ email: 1 });\nUserSchema.index({ role: 1 });\nUserSchema.index({ isActive: 1 });\n\n// Hash password before saving\nUserSchema.pre('save', async function(next) {\n  if (!this.isModified('password')) return next();\n  \n  try {\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error);\n  }\n});\n\n// Compare password method\nUserSchema.methods.comparePassword = async function(candidatePassword) {\n  return await bcrypt.compare(candidatePassword, this.password);\n};\n\n// Get public profile\nUserSchema.methods.getPublicProfile = function() {\n  const userObject = this.toObject();\n  delete userObject.password;\n  return userObject;\n};\n\nexport default mongoose.models.User || mongoose.model('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IACrC,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAmB;QACpC,MAAM;QACN,WAAW;YAAC;YAAI;SAAyC;IAC3D;IACA,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,WAAW;QACX,MAAM;QACN,OAAO;YACL;YACA;SACD;IACH;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,WAAW;YAAC;YAAG;SAAyC;QACxD,QAAQ;IACV;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAQ;SAAQ;QACvB,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,SAAS;IACX;IACA,KAAK;QACH,MAAM;QACN,WAAW;YAAC;YAAK;SAAyC;QAC1D,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,WAAW;YAAC;YAAK;SAAsD;QACvE,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,YAAY;QACV,MAAM;QACN,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IACA,eAAe;QACb,MAAM;QACN,SAAS;IACX;IACA,WAAW;QACT,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;AACd;AAEA,qCAAqC;AACrC,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAC3B,WAAW,KAAK,CAAC;IAAE,UAAU;AAAE;AAE/B,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAe,IAAI;IACxC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,0BAA0B;AAC1B,WAAW,OAAO,CAAC,eAAe,GAAG,eAAe,iBAAiB;IACnE,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AAC9D;AAEA,qBAAqB;AACrB,WAAW,OAAO,CAAC,gBAAgB,GAAG;IACpC,MAAM,aAAa,IAAI,CAAC,QAAQ;IAChC,OAAO,WAAW,QAAQ;IAC1B,OAAO;AACT;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ", "debugId": null}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/middleware/auth.js"], "sourcesContent": ["import { verifyToken } from '@/lib/jwt';\nimport User from '@/models/User';\nimport connectDB from '@/lib/mongodb';\n\nexport const authenticate = async (req) => {\n  try {\n    const authHeader = req.headers.authorization;\n    \n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      throw new Error('No token provided');\n    }\n\n    const token = authHeader.substring(7);\n    const decoded = verifyToken(token);\n\n    await connectDB();\n    const user = await User.findById(decoded.userId).select('-password');\n    \n    if (!user || !user.isActive) {\n      throw new Error('User not found or inactive');\n    }\n\n    return user;\n  } catch (error) {\n    throw new Error('Authentication failed');\n  }\n};\n\nexport const requireAuth = (handler) => {\n  return async (req, res) => {\n    try {\n      const user = await authenticate(req);\n      req.user = user;\n      return handler(req, res);\n    } catch (error) {\n      return res.status(401).json({\n        success: false,\n        message: error.message || 'Authentication required'\n      });\n    }\n  };\n};\n\nexport const requireAdmin = (handler) => {\n  return async (req, res) => {\n    try {\n      const user = await authenticate(req);\n      \n      if (user.role !== 'admin') {\n        return res.status(403).json({\n          success: false,\n          message: 'Admin access required'\n        });\n      }\n\n      req.user = user;\n      return handler(req, res);\n    } catch (error) {\n      return res.status(401).json({\n        success: false,\n        message: error.message || 'Authentication required'\n      });\n    }\n  };\n};\n\nexport const optionalAuth = async (req) => {\n  try {\n    const user = await authenticate(req);\n    return user;\n  } catch (error) {\n    return null;\n  }\n};\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AAEO,MAAM,eAAe,OAAO;IACjC,IAAI;QACF,MAAM,aAAa,IAAI,OAAO,CAAC,aAAa;QAE5C,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC;QACnC,MAAM,UAAU,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE;QAE5B,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QACd,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,QAAQ,MAAM,EAAE,MAAM,CAAC;QAExD,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,MAAM,cAAc,CAAC;IAC1B,OAAO,OAAO,KAAK;QACjB,IAAI;YACF,MAAM,OAAO,MAAM,aAAa;YAChC,IAAI,IAAI,GAAG;YACX,OAAO,QAAQ,KAAK;QACtB,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS,MAAM,OAAO,IAAI;YAC5B;QACF;IACF;AACF;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,OAAO,KAAK;QACjB,IAAI;YACF,MAAM,OAAO,MAAM,aAAa;YAEhC,IAAI,KAAK,IAAI,KAAK,SAAS;gBACzB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;oBAC1B,SAAS;oBACT,SAAS;gBACX;YACF;YAEA,IAAI,IAAI,GAAG;YACX,OAAO,QAAQ,KAAK;QACtB,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS,MAAM,OAAO,IAAI;YAC5B;QACF;IACF;AACF;AAEO,MAAM,eAAe,OAAO;IACjC,IAAI;QACF,MAAM,OAAO,MAAM,aAAa;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/app/api/files/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport File from '@/models/File';\nimport { optionalAuth } from '@/middleware/auth';\n\nexport async function GET(request) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page')) || 1;\n    const limit = parseInt(searchParams.get('limit')) || 12;\n    const search = searchParams.get('search') || '';\n    const category = searchParams.get('category') || '';\n    const subject = searchParams.get('subject') || '';\n    const fileType = searchParams.get('fileType') || '';\n    const sortBy = searchParams.get('sortBy') || 'createdAt';\n    const sortOrder = searchParams.get('sortOrder') || 'desc';\n\n    await connectDB();\n\n    // Check if user is authenticated (optional)\n    const user = await optionalAuth(request);\n\n    // Build query\n    const query = {\n      status: 'approved',\n      isPublic: true\n    };\n\n    // Add search functionality\n    if (search) {\n      query.$text = { $search: search };\n    }\n\n    // Add filters\n    if (category) {\n      query.category = category;\n    }\n\n    if (subject) {\n      query.subject = { $regex: subject, $options: 'i' };\n    }\n\n    if (fileType) {\n      query.fileType = fileType;\n    }\n\n    // Build sort object\n    const sort = {};\n    if (search && !sortBy) {\n      sort.score = { $meta: 'textScore' };\n    } else {\n      sort[sortBy] = sortOrder === 'desc' ? -1 : 1;\n    }\n\n    // Calculate skip value\n    const skip = (page - 1) * limit;\n\n    // Execute query with pagination\n    const [files, totalFiles] = await Promise.all([\n      File.find(query)\n        .populate('uploadedBy', 'name')\n        .populate('category', 'name slug')\n        .sort(sort)\n        .skip(skip)\n        .limit(limit)\n        .lean(),\n      File.countDocuments(query)\n    ]);\n\n    // Calculate pagination info\n    const totalPages = Math.ceil(totalFiles / limit);\n    const hasNextPage = page < totalPages;\n    const hasPrevPage = page > 1;\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        files,\n        pagination: {\n          currentPage: page,\n          totalPages,\n          totalFiles,\n          hasNextPage,\n          hasPrevPage,\n          limit\n        }\n      }\n    });\n\n  } catch (error) {\n    console.error('Files listing error:', error);\n    return NextResponse.json(\n      { success: false, message: 'Failed to fetch files' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IAAI,OAAO;IAC/B,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,YAAY;QACnD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,aAAa;QACrD,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;QACjD,MAAM,UAAU,aAAa,GAAG,CAAC,cAAc;QAC/C,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;QACjD,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,YAAY,aAAa,GAAG,CAAC,gBAAgB;QAEnD,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,4CAA4C;QAC5C,MAAM,OAAO,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,EAAE;QAEhC,cAAc;QACd,MAAM,QAAQ;YACZ,QAAQ;YACR,UAAU;QACZ;QAEA,2BAA2B;QAC3B,IAAI,QAAQ;YACV,MAAM,KAAK,GAAG;gBAAE,SAAS;YAAO;QAClC;QAEA,cAAc;QACd,IAAI,UAAU;YACZ,MAAM,QAAQ,GAAG;QACnB;QAEA,IAAI,SAAS;YACX,MAAM,OAAO,GAAG;gBAAE,QAAQ;gBAAS,UAAU;YAAI;QACnD;QAEA,IAAI,UAAU;YACZ,MAAM,QAAQ,GAAG;QACnB;QAEA,oBAAoB;QACpB,MAAM,OAAO,CAAC;QACd;;aAEO;YACL,IAAI,CAAC,OAAO,GAAG,cAAc,SAAS,CAAC,IAAI;QAC7C;QAEA,uBAAuB;QACvB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,gCAAgC;QAChC,MAAM,CAAC,OAAO,WAAW,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC5C,uHAAA,CAAA,UAAI,CAAC,IAAI,CAAC,OACP,QAAQ,CAAC,cAAc,QACvB,QAAQ,CAAC,YAAY,aACrB,IAAI,CAAC,MACL,IAAI,CAAC,MACL,KAAK,CAAC,OACN,IAAI;YACP,uHAAA,CAAA,UAAI,CAAC,cAAc,CAAC;SACrB;QAED,4BAA4B;QAC5B,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa;QAC1C,MAAM,cAAc,OAAO;QAC3B,MAAM,cAAc,OAAO;QAE3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ;gBACA,YAAY;oBACV,aAAa;oBACb;oBACA;oBACA;oBACA;oBACA;gBACF;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS;QAAwB,GACnD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/contexts/AuthContext.js"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useReducer, useEffect } from 'react';\n\nconst AuthContext = createContext();\n\nconst initialState = {\n  user: null,\n  token: null,\n  isLoading: true,\n  isAuthenticated: false,\n};\n\nfunction authReducer(state, action) {\n  switch (action.type) {\n    case 'LOGIN_SUCCESS':\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.accessToken,\n        isAuthenticated: true,\n        isLoading: false,\n      };\n    case 'LOGOUT':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n      };\n    case 'SET_LOADING':\n      return {\n        ...state,\n        isLoading: action.payload,\n      };\n    case 'UPDATE_USER':\n      return {\n        ...state,\n        user: { ...state.user, ...action.payload },\n      };\n    default:\n      return state;\n  }\n}\n\nexport function AuthProvider({ children }) {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  useEffect(() => {\n    // Check for stored token on mount\n    const token = localStorage.getItem('token');\n    const user = localStorage.getItem('user');\n\n    if (token && user) {\n      try {\n        const parsedUser = JSON.parse(user);\n        dispatch({\n          type: 'LOGIN_SUCCESS',\n          payload: {\n            accessToken: token,\n            user: parsedUser,\n          },\n        });\n      } catch (error) {\n        console.error('Error parsing stored user data:', error);\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n      }\n    }\n\n    dispatch({ type: 'SET_LOADING', payload: false });\n  }, []);\n\n  const login = (data) => {\n    localStorage.setItem('token', data.accessToken);\n    localStorage.setItem('user', JSON.stringify(data.user));\n    dispatch({ type: 'LOGIN_SUCCESS', payload: data });\n  };\n\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    dispatch({ type: 'LOGOUT' });\n  };\n\n  const updateUser = (userData) => {\n    const updatedUser = { ...state.user, ...userData };\n    localStorage.setItem('user', JSON.stringify(updatedUser));\n    dispatch({ type: 'UPDATE_USER', payload: userData });\n  };\n\n  const value = {\n    ...state,\n    login,\n    logout,\n    updateUser,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAIA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;AAEhC,MAAM,eAAe;IACnB,MAAM;IACN,OAAO;IACP,WAAW;IACX,iBAAiB;AACnB;AAEA,SAAS,YAAY,KAAK,EAAE,MAAM;IAChC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM,OAAO,OAAO,CAAC,IAAI;gBACzB,OAAO,OAAO,OAAO,CAAC,WAAW;gBACjC,iBAAiB;gBACjB,WAAW;YACb;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM;gBACN,OAAO;gBACP,iBAAiB;gBACjB,WAAW;YACb;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,WAAW,OAAO,OAAO;YAC3B;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM;oBAAE,GAAG,MAAM,IAAI;oBAAE,GAAG,OAAO,OAAO;gBAAC;YAC3C;QACF;YACE,OAAO;IACX;AACF;AAEO,SAAS,aAAa,EAAE,QAAQ,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,aAAa;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kCAAkC;QAClC,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,MAAM,OAAO,aAAa,OAAO,CAAC;QAElC,IAAI,SAAS,MAAM;YACjB,IAAI;gBACF,MAAM,aAAa,KAAK,KAAK,CAAC;gBAC9B,SAAS;oBACP,MAAM;oBACN,SAAS;wBACP,aAAa;wBACb,MAAM;oBACR;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;YAC1B;QACF;QAEA,SAAS;YAAE,MAAM;YAAe,SAAS;QAAM;IACjD,GAAG,EAAE;IAEL,MAAM,QAAQ,CAAC;QACb,aAAa,OAAO,CAAC,SAAS,KAAK,WAAW;QAC9C,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,KAAK,IAAI;QACrD,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAK;IAClD;IAEA,MAAM,SAAS;QACb,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,SAAS;YAAE,MAAM;QAAS;IAC5B;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc;YAAE,GAAG,MAAM,IAAI;YAAE,GAAG,QAAQ;QAAC;QACjD,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;QAC5C,SAAS;YAAE,MAAM;YAAe,SAAS;QAAS;IACpD;IAEA,MAAM,QAAQ;QACZ,GAAG,KAAK;QACR;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/components/Navigation.js"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { \n  BookOpenIcon, \n  UserIcon, \n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon,\n  Bars3Icon,\n  XMarkIcon,\n  MagnifyingGlassIcon,\n  PlusIcon\n} from '@heroicons/react/24/outline';\n\nexport default function Navigation() {\n  const { user, isAuthenticated, logout } = useAuth();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  const handleLogout = () => {\n    logout();\n    setIsMobileMenuOpen(false);\n  };\n\n  const navigation = [\n    { name: 'Browse Files', href: '/files', icon: BookOpenIcon },\n    { name: 'Categories', href: '/categories', icon: BookOpenIcon },\n  ];\n\n  const userNavigation = [\n    { name: 'My Profile', href: '/profile', icon: UserIcon },\n    { name: 'My Uploads', href: '/my-uploads', icon: BookOpenIcon },\n    { name: 'Upload File', href: '/upload', icon: PlusIcon },\n  ];\n\n  const adminNavigation = [\n    { name: 'Admin Panel', href: '/admin', icon: Cog6ToothIcon },\n  ];\n\n  return (\n    <nav className=\"bg-white shadow-lg border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* Logo and main navigation */}\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0 flex items-center\">\n              <Link href=\"/\" className=\"text-2xl font-bold text-blue-600\">\n                NOTEX\n              </Link>\n            </div>\n            <div className=\"hidden sm:ml-6 sm:flex sm:space-x-8\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\n                >\n                  <item.icon className=\"h-4 w-4 mr-2\" />\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Search bar */}\n          <div className=\"hidden sm:flex sm:items-center sm:flex-1 sm:max-w-xs sm:mx-4\">\n            <div className=\"relative w-full\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search files...\"\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              />\n            </div>\n          </div>\n\n          {/* User menu */}\n          <div className=\"hidden sm:ml-6 sm:flex sm:items-center\">\n            {isAuthenticated ? (\n              <div className=\"flex items-center space-x-4\">\n                {userNavigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium flex items-center\"\n                  >\n                    <item.icon className=\"h-4 w-4 mr-1\" />\n                    {item.name}\n                  </Link>\n                ))}\n                \n                {user?.role === 'admin' && adminNavigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium flex items-center\"\n                  >\n                    <item.icon className=\"h-4 w-4 mr-1\" />\n                    {item.name}\n                  </Link>\n                ))}\n\n                <button\n                  onClick={handleLogout}\n                  className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium flex items-center\"\n                >\n                  <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-1\" />\n                  Logout\n                </button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-4\">\n                <Link\n                  href=\"/login\"\n                  className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Login\n                </Link>\n                <Link\n                  href=\"/register\"\n                  className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n                >\n                  Register\n                </Link>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"sm:hidden flex items-center\">\n            <button\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n            >\n              {isMobileMenuOpen ? (\n                <XMarkIcon className=\"block h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"block h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isMobileMenuOpen && (\n        <div className=\"sm:hidden\">\n          <div className=\"pt-2 pb-3 space-y-1\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium\"\n                onClick={() => setIsMobileMenuOpen(false)}\n              >\n                <div className=\"flex items-center\">\n                  <item.icon className=\"h-5 w-5 mr-3\" />\n                  {item.name}\n                </div>\n              </Link>\n            ))}\n          </div>\n          \n          {isAuthenticated && (\n            <div className=\"pt-4 pb-3 border-t border-gray-200\">\n              <div className=\"space-y-1\">\n                {userNavigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium\"\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    <div className=\"flex items-center\">\n                      <item.icon className=\"h-5 w-5 mr-3\" />\n                      {item.name}\n                    </div>\n                  </Link>\n                ))}\n                \n                {user?.role === 'admin' && adminNavigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium\"\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    <div className=\"flex items-center\">\n                      <item.icon className=\"h-5 w-5 mr-3\" />\n                      {item.name}\n                    </div>\n                  </Link>\n                ))}\n\n                <button\n                  onClick={handleLogout}\n                  className=\"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium w-full text-left\"\n                >\n                  <div className=\"flex items-center\">\n                    <ArrowRightOnRectangleIcon className=\"h-5 w-5 mr-3\" />\n                    Logout\n                  </div>\n                </button>\n              </div>\n            </div>\n          )}\n\n          {!isAuthenticated && (\n            <div className=\"pt-4 pb-3 border-t border-gray-200\">\n              <div className=\"space-y-1\">\n                <Link\n                  href=\"/login\"\n                  className=\"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium\"\n                  onClick={() => setIsMobileMenuOpen(false)}\n                >\n                  Login\n                </Link>\n                <Link\n                  href=\"/register\"\n                  className=\"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium\"\n                  onClick={() => setIsMobileMenuOpen(false)}\n                >\n                  Register\n                </Link>\n              </div>\n            </div>\n          )}\n        </div>\n      )}\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAgBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAChD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,eAAe;QACnB;QACA,oBAAoB;IACtB;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAgB,MAAM;YAAU,MAAM,uNAAA,CAAA,eAAY;QAAC;QAC3D;YAAE,MAAM;YAAc,MAAM;YAAe,MAAM,uNAAA,CAAA,eAAY;QAAC;KAC/D;IAED,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAAc,MAAM;YAAY,MAAM,+MAAA,CAAA,WAAQ;QAAC;QACvD;YAAE,MAAM;YAAc,MAAM;YAAe,MAAM,uNAAA,CAAA,eAAY;QAAC;QAC9D;YAAE,MAAM;YAAe,MAAM;YAAW,MAAM,+MAAA,CAAA,WAAQ;QAAC;KACxD;IAED,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAe,MAAM;YAAU,MAAM,yNAAA,CAAA,gBAAa;QAAC;KAC5D;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAmC;;;;;;;;;;;8CAI9D,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;;8DAEV,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAYtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;;;;;;kDAEjC,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAU;sCACZ,gCACC,8OAAC;gCAAI,WAAU;;oCACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;;8DAEV,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;2CALL,KAAK,IAAI;;;;;oCASjB,MAAM,SAAS,WAAW,gBAAgB,GAAG,CAAC,CAAC,qBAC9C,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;;8DAEV,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;2CALL,KAAK,IAAI;;;;;kDASlB,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,iPAAA,CAAA,4BAAyB;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;qDAK1D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAQP,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,oBAAoB,CAAC;gCACpC,WAAU;0CAET,iCACC,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;yDAErB,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ9B,kCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,oBAAoB;0CAEnC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;;;;;;+BAPP,KAAK,IAAI;;;;;;;;;;oBAanB,iCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;wCACV,SAAS,IAAM,oBAAoB;kDAEnC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;;;;;;uCAPP,KAAK,IAAI;;;;;gCAYjB,MAAM,SAAS,WAAW,gBAAgB,GAAG,CAAC,CAAC,qBAC9C,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;wCACV,SAAS,IAAM,oBAAoB;kDAEnC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;;;;;;uCAPP,KAAK,IAAI;;;;;8CAYlB,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iPAAA,CAAA,4BAAyB;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;oBAQ/D,CAAC,iCACA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CACpC;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CACpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 645, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/app/register/page.js"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { AuthProvider } from '@/contexts/AuthContext';\nimport Navigation from '@/components/Navigation';\nimport { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';\n\nfunction RegisterPage() {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    institution: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const { login } = useAuth();\n  const router = useRouter();\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    setError('');\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setIsLoading(true);\n    setError('');\n\n    // Validation\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      setIsLoading(false);\n      return;\n    }\n\n    if (formData.password.length < 6) {\n      setError('Password must be at least 6 characters long');\n      setIsLoading(false);\n      return;\n    }\n\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          name: formData.name,\n          email: formData.email,\n          password: formData.password,\n          institution: formData.institution\n        }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        login(data.data);\n        router.push('/');\n      } else {\n        setError(data.message || 'Registration failed');\n      }\n    } catch (error) {\n      setError('Network error. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation />\n      \n      <div className=\"flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-md w-full space-y-8\">\n          <div>\n            <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n              Create your account\n            </h2>\n            <p className=\"mt-2 text-center text-sm text-gray-600\">\n              Or{' '}\n              <Link\n                href=\"/login\"\n                className=\"font-medium text-blue-600 hover:text-blue-500\"\n              >\n                sign in to your existing account\n              </Link>\n            </p>\n          </div>\n          \n          <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md\">\n                {error}\n              </div>\n            )}\n            \n            <div className=\"space-y-4\">\n              <div>\n                <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\n                  Full Name\n                </label>\n                <input\n                  id=\"name\"\n                  name=\"name\"\n                  type=\"text\"\n                  autoComplete=\"name\"\n                  required\n                  value={formData.name}\n                  onChange={handleChange}\n                  className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"Enter your full name\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                  Email address\n                </label>\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  required\n                  value={formData.email}\n                  onChange={handleChange}\n                  className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"Enter your email\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"institution\" className=\"block text-sm font-medium text-gray-700\">\n                  Institution (Optional)\n                </label>\n                <input\n                  id=\"institution\"\n                  name=\"institution\"\n                  type=\"text\"\n                  value={formData.institution}\n                  onChange={handleChange}\n                  className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"Enter your school/university\"\n                />\n              </div>\n              \n              <div>\n                <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                  Password\n                </label>\n                <div className=\"mt-1 relative\">\n                  <input\n                    id=\"password\"\n                    name=\"password\"\n                    type={showPassword ? 'text' : 'password'}\n                    autoComplete=\"new-password\"\n                    required\n                    value={formData.password}\n                    onChange={handleChange}\n                    className=\"appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                    placeholder=\"Enter your password\"\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                    onClick={() => setShowPassword(!showPassword)}\n                  >\n                    {showPassword ? (\n                      <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                    ) : (\n                      <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n\n              <div>\n                <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700\">\n                  Confirm Password\n                </label>\n                <div className=\"mt-1 relative\">\n                  <input\n                    id=\"confirmPassword\"\n                    name=\"confirmPassword\"\n                    type={showConfirmPassword ? 'text' : 'password'}\n                    autoComplete=\"new-password\"\n                    required\n                    value={formData.confirmPassword}\n                    onChange={handleChange}\n                    className=\"appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                    placeholder=\"Confirm your password\"\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                  >\n                    {showConfirmPassword ? (\n                      <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                    ) : (\n                      <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {isLoading ? 'Creating account...' : 'Create account'}\n              </button>\n            </div>\n\n            <div className=\"text-center text-sm text-gray-600\">\n              By creating an account, you agree to our{' '}\n              <Link href=\"/terms\" className=\"text-blue-600 hover:text-blue-500\">\n                Terms of Service\n              </Link>{' '}\n              and{' '}\n              <Link href=\"/privacy\" className=\"text-blue-600 hover:text-blue-500\">\n                Privacy Policy\n              </Link>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default function Register() {\n  return (\n    <AuthProvider>\n      <RegisterPage />\n    </AuthProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AARA;;;;;;;;;AAUA,SAAS;IACP,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,UAAU;QACV,iBAAiB;QACjB,aAAa;IACf;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IACxB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;QACA,SAAS;IACX;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QAET,aAAa;QACb,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;YAClD,SAAS;YACT,aAAa;YACb;QACF;QAEA,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YAChC,SAAS;YACT,aAAa;YACb;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,KAAK;oBACrB,UAAU,SAAS,QAAQ;oBAC3B,aAAa,SAAS,WAAW;gBACnC;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,KAAK,IAAI;gBACf,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,SAAS,KAAK,OAAO,IAAI;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,+HAAA,CAAA,UAAU;;;;;0BAEX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAyD;;;;;;8CAGvE,8OAAC;oCAAE,WAAU;;wCAAyC;wCACjD;sDACH,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAML,8OAAC;4BAAK,WAAU;4BAAiB,UAAU;;gCACxC,uBACC,8OAAC;oCAAI,WAAU;8CACZ;;;;;;8CAIL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAO,WAAU;8DAA0C;;;;;;8DAG1E,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAa;oDACb,QAAQ;oDACR,OAAO,SAAS,IAAI;oDACpB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAA0C;;;;;;8DAG3E,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAa;oDACb,QAAQ;oDACR,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAc,WAAU;8DAA0C;;;;;;8DAGjF,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,WAAW;oDAC3B,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAA0C;;;;;;8DAG9E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,MAAM,eAAe,SAAS;4DAC9B,cAAa;4DACb,QAAQ;4DACR,OAAO,SAAS,QAAQ;4DACxB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;sEAEd,8OAAC;4DACC,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,gBAAgB,CAAC;sEAE/B,6BACC,8OAAC,uNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;qFAExB,8OAAC,6MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAM3B,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAkB,WAAU;8DAA0C;;;;;;8DAGrF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,MAAM,sBAAsB,SAAS;4DACrC,cAAa;4DACb,QAAQ;4DACR,OAAO,SAAS,eAAe;4DAC/B,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;sEAEd,8OAAC;4DACC,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,uBAAuB,CAAC;sEAEtC,oCACC,8OAAC,uNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;qFAExB,8OAAC,6MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO7B,8OAAC;8CACC,cAAA,8OAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,YAAY,wBAAwB;;;;;;;;;;;8CAIzC,8OAAC;oCAAI,WAAU;;wCAAoC;wCACR;sDACzC,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAAoC;;;;;;wCAE1D;wCAAI;wCACR;sDACJ,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlF;AAEe,SAAS;IACtB,qBACE,8OAAC,8HAAA,CAAA,eAAY;kBACX,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}]}
import mongoose from 'mongoose';

const RatingSchema = new mongoose.Schema({
  rating: {
    type: Number,
    required: [true, 'Rating is required'],
    min: [1, 'Rating must be at least 1'],
    max: [5, 'Rating cannot be more than 5']
  },
  review: {
    type: String,
    trim: true,
    maxlength: [500, 'Review cannot be more than 500 characters'],
    default: ''
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User is required']
  },
  file: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'File',
    required: [true, 'File is required']
  },
  isApproved: {
    type: Boolean,
    default: true
  },
  moderationNotes: {
    type: String,
    default: ''
  },
  moderatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  moderatedAt: {
    type: Date,
    default: null
  }
}, {
  timestamps: true
});

// Compound index to ensure one rating per user per file
RatingSchema.index({ user: 1, file: 1 }, { unique: true });
RatingSchema.index({ file: 1, createdAt: -1 });
RatingSchema.index({ isApproved: 1 });

// Static method to calculate average rating for a file
RatingSchema.statics.calculateAverageRating = async function(fileId) {
  const stats = await this.aggregate([
    {
      $match: { file: fileId, isApproved: true }
    },
    {
      $group: {
        _id: '$file',
        averageRating: { $avg: '$rating' },
        ratingCount: { $sum: 1 }
      }
    }
  ]);

  if (stats.length > 0) {
    const File = mongoose.model('File');
    await File.findByIdAndUpdate(fileId, {
      'rating.average': Math.round(stats[0].averageRating * 10) / 10,
      'rating.count': stats[0].ratingCount
    });
  } else {
    const File = mongoose.model('File');
    await File.findByIdAndUpdate(fileId, {
      'rating.average': 0,
      'rating.count': 0
    });
  }
};

// Post-save middleware to update file rating
RatingSchema.post('save', function() {
  this.constructor.calculateAverageRating(this.file);
});

// Post-remove middleware to update file rating
RatingSchema.post('remove', function() {
  this.constructor.calculateAverageRating(this.file);
});

export default mongoose.models.Rating || mongoose.model('Rating', RatingSchema);

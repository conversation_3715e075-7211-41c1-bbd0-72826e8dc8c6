{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/contexts/AuthContext.js"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useReducer, useEffect } from 'react';\n\nconst AuthContext = createContext();\n\nconst initialState = {\n  user: null,\n  token: null,\n  isLoading: true,\n  isAuthenticated: false,\n};\n\nfunction authReducer(state, action) {\n  switch (action.type) {\n    case 'LOGIN_SUCCESS':\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.accessToken,\n        isAuthenticated: true,\n        isLoading: false,\n      };\n    case 'LOGOUT':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n      };\n    case 'SET_LOADING':\n      return {\n        ...state,\n        isLoading: action.payload,\n      };\n    case 'UPDATE_USER':\n      return {\n        ...state,\n        user: { ...state.user, ...action.payload },\n      };\n    default:\n      return state;\n  }\n}\n\nexport function AuthProvider({ children }) {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  useEffect(() => {\n    // Check for stored token on mount\n    const token = localStorage.getItem('token');\n    const user = localStorage.getItem('user');\n\n    if (token && user) {\n      try {\n        const parsedUser = JSON.parse(user);\n        dispatch({\n          type: 'LOGIN_SUCCESS',\n          payload: {\n            accessToken: token,\n            user: parsedUser,\n          },\n        });\n      } catch (error) {\n        console.error('Error parsing stored user data:', error);\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n      }\n    }\n\n    dispatch({ type: 'SET_LOADING', payload: false });\n  }, []);\n\n  const login = (data) => {\n    localStorage.setItem('token', data.accessToken);\n    localStorage.setItem('user', JSON.stringify(data.user));\n    dispatch({ type: 'LOGIN_SUCCESS', payload: data });\n  };\n\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    dispatch({ type: 'LOGOUT' });\n  };\n\n  const updateUser = (userData) => {\n    const updatedUser = { ...state.user, ...userData };\n    localStorage.setItem('user', JSON.stringify(updatedUser));\n    dispatch({ type: 'UPDATE_USER', payload: userData });\n  };\n\n  const value = {\n    ...state,\n    login,\n    logout,\n    updateUser,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAIA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;AAEhC,MAAM,eAAe;IACnB,MAAM;IACN,OAAO;IACP,WAAW;IACX,iBAAiB;AACnB;AAEA,SAAS,YAAY,KAAK,EAAE,MAAM;IAChC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM,OAAO,OAAO,CAAC,IAAI;gBACzB,OAAO,OAAO,OAAO,CAAC,WAAW;gBACjC,iBAAiB;gBACjB,WAAW;YACb;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM;gBACN,OAAO;gBACP,iBAAiB;gBACjB,WAAW;YACb;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,WAAW,OAAO,OAAO;YAC3B;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM;oBAAE,GAAG,MAAM,IAAI;oBAAE,GAAG,OAAO,OAAO;gBAAC;YAC3C;QACF;YACE,OAAO;IACX;AACF;AAEO,SAAS,aAAa,EAAE,QAAQ,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,aAAa;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kCAAkC;QAClC,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,MAAM,OAAO,aAAa,OAAO,CAAC;QAElC,IAAI,SAAS,MAAM;YACjB,IAAI;gBACF,MAAM,aAAa,KAAK,KAAK,CAAC;gBAC9B,SAAS;oBACP,MAAM;oBACN,SAAS;wBACP,aAAa;wBACb,MAAM;oBACR;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;YAC1B;QACF;QAEA,SAAS;YAAE,MAAM;YAAe,SAAS;QAAM;IACjD,GAAG,EAAE;IAEL,MAAM,QAAQ,CAAC;QACb,aAAa,OAAO,CAAC,SAAS,KAAK,WAAW;QAC9C,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,KAAK,IAAI;QACrD,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAK;IAClD;IAEA,MAAM,SAAS;QACb,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,SAAS;YAAE,MAAM;QAAS;IAC5B;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc;YAAE,GAAG,MAAM,IAAI;YAAE,GAAG,QAAQ;QAAC;QACjD,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;QAC5C,SAAS;YAAE,MAAM;YAAe,SAAS;QAAS;IACpD;IAEA,MAAM,QAAQ;QACZ,GAAG,KAAK;QACR;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/components/Navigation.js"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { \n  BookOpenIcon, \n  UserIcon, \n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon,\n  Bars3Icon,\n  XMarkIcon,\n  MagnifyingGlassIcon,\n  PlusIcon\n} from '@heroicons/react/24/outline';\n\nexport default function Navigation() {\n  const { user, isAuthenticated, logout } = useAuth();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  const handleLogout = () => {\n    logout();\n    setIsMobileMenuOpen(false);\n  };\n\n  const navigation = [\n    { name: 'Browse Files', href: '/files', icon: BookOpenIcon },\n    { name: 'Categories', href: '/categories', icon: BookOpenIcon },\n  ];\n\n  const userNavigation = [\n    { name: 'My Profile', href: '/profile', icon: UserIcon },\n    { name: 'My Uploads', href: '/my-uploads', icon: BookOpenIcon },\n    { name: 'Upload File', href: '/upload', icon: PlusIcon },\n  ];\n\n  const adminNavigation = [\n    { name: 'Admin Panel', href: '/admin', icon: Cog6ToothIcon },\n  ];\n\n  return (\n    <nav className=\"bg-white shadow-lg border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* Logo and main navigation */}\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0 flex items-center\">\n              <Link href=\"/\" className=\"text-2xl font-bold text-blue-600\">\n                NOTEX\n              </Link>\n            </div>\n            <div className=\"hidden sm:ml-6 sm:flex sm:space-x-8\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\n                >\n                  <item.icon className=\"h-4 w-4 mr-2\" />\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Search bar */}\n          <div className=\"hidden sm:flex sm:items-center sm:flex-1 sm:max-w-xs sm:mx-4\">\n            <div className=\"relative w-full\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search files...\"\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              />\n            </div>\n          </div>\n\n          {/* User menu */}\n          <div className=\"hidden sm:ml-6 sm:flex sm:items-center\">\n            {isAuthenticated ? (\n              <div className=\"flex items-center space-x-4\">\n                {userNavigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium flex items-center\"\n                  >\n                    <item.icon className=\"h-4 w-4 mr-1\" />\n                    {item.name}\n                  </Link>\n                ))}\n                \n                {user?.role === 'admin' && adminNavigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium flex items-center\"\n                  >\n                    <item.icon className=\"h-4 w-4 mr-1\" />\n                    {item.name}\n                  </Link>\n                ))}\n\n                <button\n                  onClick={handleLogout}\n                  className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium flex items-center\"\n                >\n                  <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-1\" />\n                  Logout\n                </button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-4\">\n                <Link\n                  href=\"/login\"\n                  className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Login\n                </Link>\n                <Link\n                  href=\"/register\"\n                  className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n                >\n                  Register\n                </Link>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"sm:hidden flex items-center\">\n            <button\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n            >\n              {isMobileMenuOpen ? (\n                <XMarkIcon className=\"block h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"block h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isMobileMenuOpen && (\n        <div className=\"sm:hidden\">\n          <div className=\"pt-2 pb-3 space-y-1\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium\"\n                onClick={() => setIsMobileMenuOpen(false)}\n              >\n                <div className=\"flex items-center\">\n                  <item.icon className=\"h-5 w-5 mr-3\" />\n                  {item.name}\n                </div>\n              </Link>\n            ))}\n          </div>\n          \n          {isAuthenticated && (\n            <div className=\"pt-4 pb-3 border-t border-gray-200\">\n              <div className=\"space-y-1\">\n                {userNavigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium\"\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    <div className=\"flex items-center\">\n                      <item.icon className=\"h-5 w-5 mr-3\" />\n                      {item.name}\n                    </div>\n                  </Link>\n                ))}\n                \n                {user?.role === 'admin' && adminNavigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium\"\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    <div className=\"flex items-center\">\n                      <item.icon className=\"h-5 w-5 mr-3\" />\n                      {item.name}\n                    </div>\n                  </Link>\n                ))}\n\n                <button\n                  onClick={handleLogout}\n                  className=\"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium w-full text-left\"\n                >\n                  <div className=\"flex items-center\">\n                    <ArrowRightOnRectangleIcon className=\"h-5 w-5 mr-3\" />\n                    Logout\n                  </div>\n                </button>\n              </div>\n            </div>\n          )}\n\n          {!isAuthenticated && (\n            <div className=\"pt-4 pb-3 border-t border-gray-200\">\n              <div className=\"space-y-1\">\n                <Link\n                  href=\"/login\"\n                  className=\"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium\"\n                  onClick={() => setIsMobileMenuOpen(false)}\n                >\n                  Login\n                </Link>\n                <Link\n                  href=\"/register\"\n                  className=\"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium\"\n                  onClick={() => setIsMobileMenuOpen(false)}\n                >\n                  Register\n                </Link>\n              </div>\n            </div>\n          )}\n        </div>\n      )}\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAgBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAChD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,eAAe;QACnB;QACA,oBAAoB;IACtB;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAgB,MAAM;YAAU,MAAM,uNAAA,CAAA,eAAY;QAAC;QAC3D;YAAE,MAAM;YAAc,MAAM;YAAe,MAAM,uNAAA,CAAA,eAAY;QAAC;KAC/D;IAED,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAAc,MAAM;YAAY,MAAM,+MAAA,CAAA,WAAQ;QAAC;QACvD;YAAE,MAAM;YAAc,MAAM;YAAe,MAAM,uNAAA,CAAA,eAAY;QAAC;QAC9D;YAAE,MAAM;YAAe,MAAM;YAAW,MAAM,+MAAA,CAAA,WAAQ;QAAC;KACxD;IAED,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAe,MAAM;YAAU,MAAM,yNAAA,CAAA,gBAAa;QAAC;KAC5D;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAmC;;;;;;;;;;;8CAI9D,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;;8DAEV,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAYtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;;;;;;kDAEjC,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAU;sCACZ,gCACC,8OAAC;gCAAI,WAAU;;oCACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;;8DAEV,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;2CALL,KAAK,IAAI;;;;;oCASjB,MAAM,SAAS,WAAW,gBAAgB,GAAG,CAAC,CAAC,qBAC9C,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;;8DAEV,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;2CALL,KAAK,IAAI;;;;;kDASlB,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,iPAAA,CAAA,4BAAyB;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;qDAK1D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAQP,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,oBAAoB,CAAC;gCACpC,WAAU;0CAET,iCACC,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;yDAErB,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ9B,kCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,oBAAoB;0CAEnC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;;;;;;+BAPP,KAAK,IAAI;;;;;;;;;;oBAanB,iCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;wCACV,SAAS,IAAM,oBAAoB;kDAEnC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;;;;;;uCAPP,KAAK,IAAI;;;;;gCAYjB,MAAM,SAAS,WAAW,gBAAgB,GAAG,CAAC,CAAC,qBAC9C,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;wCACV,SAAS,IAAM,oBAAoB;kDAEnC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;;;;;;uCAPP,KAAK,IAAI;;;;;8CAYlB,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iPAAA,CAAA,4BAAyB;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;oBAQ/D,CAAC,iCACA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CACpC;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CACpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 645, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/components/FileCard.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { formatDistanceToNow } from 'date-fns';\nimport { \n  EyeIcon, \n  ArrowDownTrayIcon, \n  StarIcon,\n  DocumentIcon,\n  PhotoIcon\n} from '@heroicons/react/24/outline';\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';\n\nexport default function FileCard({ file }) {\n  const getFileIcon = (fileType) => {\n    const imageTypes = ['jpg', 'jpeg', 'png', 'gif'];\n    if (imageTypes.includes(fileType.toLowerCase())) {\n      return <PhotoIcon className=\"h-8 w-8 text-blue-500\" />;\n    }\n    return <DocumentIcon className=\"h-8 w-8 text-gray-500\" />;\n  };\n\n  const formatFileSize = (bytes) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const renderStars = (rating) => {\n    const stars = [];\n    const fullStars = Math.floor(rating);\n    const hasHalfStar = rating % 1 !== 0;\n\n    for (let i = 0; i < 5; i++) {\n      if (i < fullStars) {\n        stars.push(\n          <StarIconSolid key={i} className=\"h-4 w-4 text-yellow-400\" />\n        );\n      } else if (i === fullStars && hasHalfStar) {\n        stars.push(\n          <div key={i} className=\"relative\">\n            <StarIcon className=\"h-4 w-4 text-gray-300\" />\n            <StarIconSolid className=\"h-4 w-4 text-yellow-400 absolute top-0 left-0 w-1/2 overflow-hidden\" />\n          </div>\n        );\n      } else {\n        stars.push(\n          <StarIcon key={i} className=\"h-4 w-4 text-gray-300\" />\n        );\n      }\n    }\n\n    return stars;\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden\">\n      {/* File preview/thumbnail */}\n      <div className=\"h-48 bg-gray-50 flex items-center justify-center\">\n        {file.thumbnailUrl ? (\n          <img\n            src={file.thumbnailUrl}\n            alt={file.title}\n            className=\"h-full w-full object-cover\"\n          />\n        ) : (\n          <div className=\"flex flex-col items-center justify-center text-gray-400\">\n            {getFileIcon(file.fileType)}\n            <span className=\"mt-2 text-sm font-medium uppercase\">\n              {file.fileType}\n            </span>\n          </div>\n        )}\n      </div>\n\n      {/* File information */}\n      <div className=\"p-4\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex-1 min-w-0\">\n            <Link href={`/files/${file._id}`}>\n              <h3 className=\"text-lg font-semibold text-gray-900 hover:text-blue-600 cursor-pointer truncate\">\n                {file.title}\n              </h3>\n            </Link>\n            <p className=\"text-sm text-gray-600 mt-1 line-clamp-2\">\n              {file.description}\n            </p>\n          </div>\n        </div>\n\n        {/* File metadata */}\n        <div className=\"mt-3 space-y-2\">\n          <div className=\"flex items-center justify-between text-sm text-gray-500\">\n            <span className=\"font-medium\">{file.subject}</span>\n            <span>{formatFileSize(file.fileSize)}</span>\n          </div>\n          \n          <div className=\"flex items-center justify-between text-sm text-gray-500\">\n            <span>{file.course}</span>\n            <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs\">\n              {file.category?.name}\n            </span>\n          </div>\n\n          {/* Author and upload date */}\n          <div className=\"flex items-center justify-between text-sm text-gray-500\">\n            <span>by {file.uploadedBy?.name}</span>\n            <span>{formatDistanceToNow(new Date(file.createdAt), { addSuffix: true })}</span>\n          </div>\n        </div>\n\n        {/* Rating */}\n        {file.rating && file.rating.count > 0 && (\n          <div className=\"mt-3 flex items-center\">\n            <div className=\"flex items-center\">\n              {renderStars(file.rating.average)}\n            </div>\n            <span className=\"ml-2 text-sm text-gray-600\">\n              {file.rating.average.toFixed(1)} ({file.rating.count} reviews)\n            </span>\n          </div>\n        )}\n\n        {/* Tags */}\n        {file.tags && file.tags.length > 0 && (\n          <div className=\"mt-3\">\n            <div className=\"flex flex-wrap gap-1\">\n              {file.tags.slice(0, 3).map((tag, index) => (\n                <span\n                  key={index}\n                  className=\"inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded\"\n                >\n                  {tag}\n                </span>\n              ))}\n              {file.tags.length > 3 && (\n                <span className=\"inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded\">\n                  +{file.tags.length - 3} more\n                </span>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Stats and actions */}\n        <div className=\"mt-4 flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n            <div className=\"flex items-center\">\n              <EyeIcon className=\"h-4 w-4 mr-1\" />\n              {file.viewCount || 0}\n            </div>\n            <div className=\"flex items-center\">\n              <ArrowDownTrayIcon className=\"h-4 w-4 mr-1\" />\n              {file.downloadCount || 0}\n            </div>\n          </div>\n\n          <Link\n            href={`/files/${file._id}`}\n            className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\n          >\n            View Details\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAXA;;;;;;AAae,SAAS,SAAS,EAAE,IAAI,EAAE;IACvC,MAAM,cAAc,CAAC;QACnB,MAAM,aAAa;YAAC;YAAO;YAAQ;YAAO;SAAM;QAChD,IAAI,WAAW,QAAQ,CAAC,SAAS,WAAW,KAAK;YAC/C,qBAAO,8OAAC,iNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAC9B;QACA,qBAAO,8OAAC,uNAAA,CAAA,eAAY;YAAC,WAAU;;;;;;IACjC;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAQ,EAAE;QAChB,MAAM,YAAY,KAAK,KAAK,CAAC;QAC7B,MAAM,cAAc,SAAS,MAAM;QAEnC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,IAAI,IAAI,WAAW;gBACjB,MAAM,IAAI,eACR,8OAAC,6MAAA,CAAA,WAAa;oBAAS,WAAU;mBAAb;;;;;YAExB,OAAO,IAAI,MAAM,aAAa,aAAa;gBACzC,MAAM,IAAI,eACR,8OAAC;oBAAY,WAAU;;sCACrB,8OAAC,+MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC,6MAAA,CAAA,WAAa;4BAAC,WAAU;;;;;;;mBAFjB;;;;;YAKd,OAAO;gBACL,MAAM,IAAI,eACR,8OAAC,+MAAA,CAAA,WAAQ;oBAAS,WAAU;mBAAb;;;;;YAEnB;QACF;QAEA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACZ,KAAK,YAAY,iBAChB,8OAAC;oBACC,KAAK,KAAK,YAAY;oBACtB,KAAK,KAAK,KAAK;oBACf,WAAU;;;;;yCAGZ,8OAAC;oBAAI,WAAU;;wBACZ,YAAY,KAAK,QAAQ;sCAC1B,8OAAC;4BAAK,WAAU;sCACb,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAOtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;8CAC9B,cAAA,8OAAC;wCAAG,WAAU;kDACX,KAAK,KAAK;;;;;;;;;;;8CAGf,8OAAC;oCAAE,WAAU;8CACV,KAAK,WAAW;;;;;;;;;;;;;;;;;kCAMvB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAe,KAAK,OAAO;;;;;;kDAC3C,8OAAC;kDAAM,eAAe,KAAK,QAAQ;;;;;;;;;;;;0CAGrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAM,KAAK,MAAM;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDACb,KAAK,QAAQ,EAAE;;;;;;;;;;;;0CAKpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAK;4CAAI,KAAK,UAAU,EAAE;;;;;;;kDAC3B,8OAAC;kDAAM,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,KAAK,SAAS,GAAG;4CAAE,WAAW;wCAAK;;;;;;;;;;;;;;;;;;oBAK1E,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,KAAK,GAAG,mBAClC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,YAAY,KAAK,MAAM,CAAC,OAAO;;;;;;0CAElC,8OAAC;gCAAK,WAAU;;oCACb,KAAK,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;oCAAG;oCAAG,KAAK,MAAM,CAAC,KAAK;oCAAC;;;;;;;;;;;;;oBAM1D,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;gCAMR,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,8OAAC;oCAAK,WAAU;;wCAAmE;wCAC/E,KAAK,IAAI,CAAC,MAAM,GAAG;wCAAE;;;;;;;;;;;;;;;;;;kCAQjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAClB,KAAK,SAAS,IAAI;;;;;;;kDAErB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iOAAA,CAAA,oBAAiB;gDAAC,WAAU;;;;;;4CAC5B,KAAK,aAAa,IAAI;;;;;;;;;;;;;0CAI3B,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;gCAC1B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 1056, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/components/Loading.js"], "sourcesContent": ["export default function Loading({ size = 'md', text = 'Loading...' }) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12',\n    xl: 'h-16 w-16'\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center p-8\">\n      <div className={`animate-spin rounded-full border-b-2 border-blue-600 ${sizeClasses[size]}`}></div>\n      {text && (\n        <p className=\"mt-4 text-gray-600 text-sm\">{text}</p>\n      )}\n    </div>\n  );\n}\n\nexport function LoadingCard() {\n  return (\n    <div className=\"bg-white rounded-lg shadow-md overflow-hidden animate-pulse\">\n      <div className=\"h-48 bg-gray-200\"></div>\n      <div className=\"p-4\">\n        <div className=\"h-6 bg-gray-200 rounded mb-2\"></div>\n        <div className=\"h-4 bg-gray-200 rounded mb-4\"></div>\n        <div className=\"space-y-2\">\n          <div className=\"h-3 bg-gray-200 rounded\"></div>\n          <div className=\"h-3 bg-gray-200 rounded\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-3/4\"></div>\n        </div>\n        <div className=\"mt-4 flex justify-between\">\n          <div className=\"h-4 bg-gray-200 rounded w-1/3\"></div>\n          <div className=\"h-8 bg-gray-200 rounded w-1/4\"></div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport function LoadingGrid({ count = 6 }) {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n      {Array.from({ length: count }).map((_, index) => (\n        <LoadingCard key={index} />\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAAe,SAAS,QAAQ,EAAE,OAAO,IAAI,EAAE,OAAO,YAAY,EAAE;IAClE,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAW,CAAC,qDAAqD,EAAE,WAAW,CAAC,KAAK,EAAE;;;;;;YAC1F,sBACC,8OAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKzB;AAEO,SAAS,YAAY,EAAE,QAAQ,CAAC,EAAE;IACvC,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,8OAAC,iBAAiB;;;;;;;;;;AAI1B", "debugId": null}}, {"offset": {"line": 1209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/app/page.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useState, useEffect } from 'react';\nimport {\n  BookOpenIcon,\n  UserGroupIcon,\n  CloudArrowUpIcon,\n  MagnifyingGlassIcon,\n  StarIcon,\n  ArrowRightIcon\n} from '@heroicons/react/24/outline';\nimport { AuthProvider } from '@/contexts/AuthContext';\nimport Navigation from '@/components/Navigation';\nimport FileCard from '@/components/FileCard';\nimport { LoadingGrid } from '@/components/Loading';\n\nexport default function Home() {\n  const [recentFiles, setRecentFiles] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    fetchRecentFiles();\n  }, []);\n\n  const fetchRecentFiles = async () => {\n    try {\n      const response = await fetch('/api/files?limit=6&sortBy=createdAt&sortOrder=desc');\n      if (response.ok) {\n        const data = await response.json();\n        setRecentFiles(data.data.files);\n      }\n    } catch (error) {\n      console.error('Error fetching recent files:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const features = [\n    {\n      icon: CloudArrowUpIcon,\n      title: 'Easy Upload',\n      description: 'Upload your lecture notes and e-books with detailed metadata and categorization.'\n    },\n    {\n      icon: MagnifyingGlassIcon,\n      title: 'Smart Search',\n      description: 'Find exactly what you need with our advanced search and filtering capabilities.'\n    },\n    {\n      icon: UserGroupIcon,\n      title: 'Community Driven',\n      description: 'Share knowledge with fellow students and educators in a collaborative environment.'\n    },\n    {\n      icon: StarIcon,\n      title: 'Quality Content',\n      description: 'All uploads are moderated to ensure high-quality educational resources.'\n    }\n  ];\n\n  return (\n    <AuthProvider>\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navigation />\n\n        {/* Hero Section */}\n        <div className=\"bg-gradient-to-r from-blue-600 to-blue-800 text-white\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24\">\n            <div className=\"text-center\">\n              <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n                Welcome to <span className=\"text-blue-200\">NOTEX</span>\n              </h1>\n              <p className=\"text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto\">\n                Your comprehensive platform for sharing and discovering educational resources.\n                Upload, browse, and download lecture notes and e-books with ease.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <Link\n                  href=\"/files\"\n                  className=\"bg-white text-blue-600 hover:bg-blue-50 px-8 py-3 rounded-lg font-semibold text-lg transition-colors duration-200 flex items-center justify-center\"\n                >\n                  <BookOpenIcon className=\"h-5 w-5 mr-2\" />\n                  Browse Files\n                </Link>\n                <Link\n                  href=\"/upload\"\n                  className=\"border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3 rounded-lg font-semibold text-lg transition-colors duration-200 flex items-center justify-center\"\n                >\n                  <CloudArrowUpIcon className=\"h-5 w-5 mr-2\" />\n                  Upload Files\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Features Section */}\n        <div className=\"py-16 bg-white\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n                Why Choose NOTEX?\n              </h2>\n              <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n                Built specifically for students and educators to share knowledge and resources effectively.\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n              {features.map((feature, index) => (\n                <div key={index} className=\"text-center\">\n                  <div className=\"bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\">\n                    <feature.icon className=\"h-8 w-8 text-blue-600\" />\n                  </div>\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                    {feature.title}\n                  </h3>\n                  <p className=\"text-gray-600\">\n                    {feature.description}\n                  </p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Recent Files Section */}\n        <div className=\"py-16 bg-gray-50\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex items-center justify-between mb-8\">\n              <div>\n                <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                  Recently Added\n                </h2>\n                <p className=\"text-gray-600\">\n                  Discover the latest educational resources shared by our community\n                </p>\n              </div>\n              <Link\n                href=\"/files\"\n                className=\"text-blue-600 hover:text-blue-700 font-medium flex items-center\"\n              >\n                View All\n                <ArrowRightIcon className=\"h-4 w-4 ml-1\" />\n              </Link>\n            </div>\n\n            {isLoading ? (\n              <LoadingGrid count={6} />\n            ) : recentFiles.length > 0 ? (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {recentFiles.map((file) => (\n                  <FileCard key={file._id} file={file} />\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-12\">\n                <BookOpenIcon className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                  No files yet\n                </h3>\n                <p className=\"text-gray-600 mb-4\">\n                  Be the first to share educational resources with the community!\n                </p>\n                <Link\n                  href=\"/upload\"\n                  className=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium\"\n                >\n                  Upload First File\n                </Link>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"bg-blue-600 text-white py-16\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <h2 className=\"text-3xl font-bold mb-4\">\n              Ready to Start Sharing Knowledge?\n            </h2>\n            <p className=\"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\">\n              Join our community of students and educators. Share your notes and discover amazing resources.\n            </p>\n            <Link\n              href=\"/register\"\n              className=\"bg-white text-blue-600 hover:bg-blue-50 px-8 py-3 rounded-lg font-semibold text-lg transition-colors duration-200 inline-flex items-center\"\n            >\n              Get Started Today\n              <ArrowRightIcon className=\"h-5 w-5 ml-2\" />\n            </Link>\n          </div>\n        </div>\n\n        {/* Footer */}\n        <footer className=\"bg-gray-800 text-white py-8\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center\">\n              <h3 className=\"text-2xl font-bold mb-2\">NOTEX</h3>\n              <p className=\"text-gray-400\">\n                Empowering education through shared knowledge\n              </p>\n            </div>\n          </div>\n        </footer>\n      </div>\n    </AuthProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;AACA;AAfA;;;;;;;;;AAiBe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,eAAe,KAAK,IAAI,CAAC,KAAK;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW;QACf;YACE,MAAM,+NAAA,CAAA,mBAAgB;YACtB,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,qOAAA,CAAA,sBAAmB;YACzB,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,yNAAA,CAAA,gBAAa;YACnB,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,+MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC,8HAAA,CAAA,eAAY;kBACX,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,+HAAA,CAAA,UAAU;;;;;8BAGX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAsC;sDACvC,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAE7C,8OAAC;oCAAE,WAAU;8CAA2D;;;;;;8CAIxE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,uNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAG3C,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,+NAAA,CAAA,mBAAgB;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASvD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAA0C;;;;;;;;;;;;0CAKzD,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,QAAQ,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAE1B,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,8OAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;;uCARd;;;;;;;;;;;;;;;;;;;;;8BAiBlB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DAGtD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAI/B,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;4CACX;0DAEC,8OAAC,2NAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;;;;;;;;;;;;;4BAI7B,0BACC,8OAAC,4HAAA,CAAA,cAAW;gCAAC,OAAO;;;;;uCAClB,YAAY,MAAM,GAAG,kBACvB,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC,6HAAA,CAAA,UAAQ;wCAAgB,MAAM;uCAAhB,KAAK,GAAG;;;;;;;;;qDAI3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,uNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;8BAST,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CAGxC,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAG5D,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;oCACX;kDAEC,8OAAC,2NAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAMhC,8OAAC;oBAAO,WAAU;8BAChB,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3C", "debugId": null}}]}
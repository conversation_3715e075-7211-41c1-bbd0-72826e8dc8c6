import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Rating from '@/models/Rating';
import File from '@/models/File';
import { requireAuth } from '@/middleware/auth';

async function createRatingHandler(request) {
  try {
    const { rating, review, fileId } = await request.json();

    if (!rating || !fileId) {
      return NextResponse.json(
        { success: false, message: 'Rating and file ID are required' },
        { status: 400 }
      );
    }

    if (rating < 1 || rating > 5) {
      return NextResponse.json(
        { success: false, message: 'Rating must be between 1 and 5' },
        { status: 400 }
      );
    }

    await connectDB();

    // Check if file exists and is accessible
    const file = await File.findById(fileId);
    if (!file) {
      return NextResponse.json(
        { success: false, message: 'File not found' },
        { status: 404 }
      );
    }

    if (file.status !== 'approved' || !file.isPublic) {
      return NextResponse.json(
        { success: false, message: 'Cannot rate this file' },
        { status: 403 }
      );
    }

    // Check if user has already rated this file
    const existingRating = await Rating.findOne({
      user: request.user._id,
      file: fileId
    });

    let ratingDoc;

    if (existingRating) {
      // Update existing rating
      existingRating.rating = rating;
      existingRating.review = review || '';
      ratingDoc = await existingRating.save();
    } else {
      // Create new rating
      ratingDoc = await Rating.create({
        rating,
        review: review || '',
        user: request.user._id,
        file: fileId
      });
    }

    // Populate user information
    await ratingDoc.populate('user', 'name avatar');

    return NextResponse.json({
      success: true,
      message: existingRating ? 'Rating updated successfully' : 'Rating added successfully',
      data: { rating: ratingDoc }
    }, { status: existingRating ? 200 : 201 });

  } catch (error) {
    console.error('Rating creation error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to add rating' },
      { status: 500 }
    );
  }
}

export const POST = requireAuth(createRatingHandler);

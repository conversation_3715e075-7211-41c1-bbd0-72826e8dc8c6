'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';
import {
  BookOpenIcon,
  UserGroupIcon,
  CloudArrowUpIcon,
  MagnifyingGlassIcon,
  StarIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';
import { AuthProvider } from '@/contexts/AuthContext';
import Navigation from '@/components/Navigation';
import FileCard from '@/components/FileCard';
import { LoadingGrid } from '@/components/Loading';

export default function Home() {
  const [recentFiles, setRecentFiles] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchRecentFiles();
  }, []);

  const fetchRecentFiles = async () => {
    try {
      const response = await fetch('/api/files?limit=6&sortBy=createdAt&sortOrder=desc');
      if (response.ok) {
        const data = await response.json();
        setRecentFiles(data.data.files);
      }
    } catch (error) {
      console.error('Error fetching recent files:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const features = [
    {
      icon: CloudArrowUpIcon,
      title: 'Easy Upload',
      description: 'Upload your lecture notes and e-books with detailed metadata and categorization.'
    },
    {
      icon: MagnifyingGlassIcon,
      title: 'Smart Search',
      description: 'Find exactly what you need with our advanced search and filtering capabilities.'
    },
    {
      icon: UserGroupIcon,
      title: 'Community Driven',
      description: 'Share knowledge with fellow students and educators in a collaborative environment.'
    },
    {
      icon: StarIcon,
      title: 'Quality Content',
      description: 'All uploads are moderated to ensure high-quality educational resources.'
    }
  ];

  return (
    <AuthProvider>
      <div className="min-h-screen bg-gray-50">
        <Navigation />

        {/* Hero Section */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div className="text-center">
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Welcome to <span className="text-blue-200">NOTEX</span>
              </h1>
              <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
                Your comprehensive platform for sharing and discovering educational resources.
                Upload, browse, and download lecture notes and e-books with ease.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/files"
                  className="bg-white text-blue-600 hover:bg-blue-50 px-8 py-3 rounded-lg font-semibold text-lg transition-colors duration-200 flex items-center justify-center"
                >
                  <BookOpenIcon className="h-5 w-5 mr-2" />
                  Browse Files
                </Link>
                <Link
                  href="/upload"
                  className="border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3 rounded-lg font-semibold text-lg transition-colors duration-200 flex items-center justify-center"
                >
                  <CloudArrowUpIcon className="h-5 w-5 mr-2" />
                  Upload Files
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Why Choose NOTEX?
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Built specifically for students and educators to share knowledge and resources effectively.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {features.map((feature, index) => (
                <div key={index} className="text-center">
                  <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <feature.icon className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600">
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Files Section */}
        <div className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between mb-8">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-2">
                  Recently Added
                </h2>
                <p className="text-gray-600">
                  Discover the latest educational resources shared by our community
                </p>
              </div>
              <Link
                href="/files"
                className="text-blue-600 hover:text-blue-700 font-medium flex items-center"
              >
                View All
                <ArrowRightIcon className="h-4 w-4 ml-1" />
              </Link>
            </div>

            {isLoading ? (
              <LoadingGrid count={6} />
            ) : recentFiles.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {recentFiles.map((file) => (
                  <FileCard key={file._id} file={file} />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <BookOpenIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No files yet
                </h3>
                <p className="text-gray-600 mb-4">
                  Be the first to share educational resources with the community!
                </p>
                <Link
                  href="/upload"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium"
                >
                  Upload First File
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-blue-600 text-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold mb-4">
              Ready to Start Sharing Knowledge?
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Join our community of students and educators. Share your notes and discover amazing resources.
            </p>
            <Link
              href="/register"
              className="bg-white text-blue-600 hover:bg-blue-50 px-8 py-3 rounded-lg font-semibold text-lg transition-colors duration-200 inline-flex items-center"
            >
              Get Started Today
              <ArrowRightIcon className="h-5 w-5 ml-2" />
            </Link>
          </div>
        </div>

        {/* Footer */}
        <footer className="bg-gray-800 text-white py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h3 className="text-2xl font-bold mb-2">NOTEX</h3>
              <p className="text-gray-400">
                Empowering education through shared knowledge
              </p>
            </div>
          </div>
        </footer>
      </div>
    </AuthProvider>
  );
}

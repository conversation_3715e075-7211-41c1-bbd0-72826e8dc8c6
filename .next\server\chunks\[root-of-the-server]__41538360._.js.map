{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/lib/mongodb.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAEA;;;;CAIC,GACD,IAAI,SAAS,4CAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,4CAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/models/Category.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst CategorySchema = new mongoose.Schema({\n  name: {\n    type: String,\n    required: [true, 'Category name is required'],\n    unique: true,\n    trim: true,\n    maxlength: [50, 'Category name cannot be more than 50 characters']\n  },\n  slug: {\n    type: String,\n    required: [true, 'Category slug is required'],\n    unique: true,\n    lowercase: true,\n    trim: true\n  },\n  description: {\n    type: String,\n    trim: true,\n    maxlength: [500, 'Description cannot be more than 500 characters'],\n    default: ''\n  },\n  icon: {\n    type: String,\n    default: 'folder'\n  },\n  color: {\n    type: String,\n    default: '#3B82F6',\n    match: [/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Please enter a valid hex color']\n  },\n  isActive: {\n    type: Boolean,\n    default: true\n  },\n  fileCount: {\n    type: Number,\n    default: 0\n  },\n  order: {\n    type: Number,\n    default: 0\n  },\n  createdBy: {\n    type: mongoose.Schema.Types.ObjectId,\n    ref: 'User',\n    required: [true, 'Creator is required']\n  }\n}, {\n  timestamps: true\n});\n\n// Indexes\nCategorySchema.index({ slug: 1 });\nCategorySchema.index({ isActive: 1 });\nCategorySchema.index({ order: 1 });\n\n// Pre-save middleware to generate slug\nCategorySchema.pre('save', function(next) {\n  if (this.isModified('name')) {\n    this.slug = this.name\n      .toLowerCase()\n      .replace(/[^a-z0-9]+/g, '-')\n      .replace(/(^-|-$)/g, '');\n  }\n  next();\n});\n\n// Virtual for files\nCategorySchema.virtual('files', {\n  ref: 'File',\n  localField: '_id',\n  foreignField: 'category'\n});\n\n// Ensure virtual fields are serialized\nCategorySchema.set('toJSON', { virtuals: true });\n\nexport default mongoose.models.Category || mongoose.model('Category', CategorySchema);\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,iBAAiB,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IACzC,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAA4B;QAC7C,QAAQ;QACR,MAAM;QACN,WAAW;YAAC;YAAI;SAAkD;IACpE;IACA,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAA4B;QAC7C,QAAQ;QACR,WAAW;QACX,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAK;SAAiD;QAClE,SAAS;IACX;IACA,MAAM;QACJ,MAAM;QACN,SAAS;IACX;IACA,OAAO;QACL,MAAM;QACN,SAAS;QACT,OAAO;YAAC;YAAsC;SAAiC;IACjF;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,WAAW;QACT,MAAM;QACN,SAAS;IACX;IACA,OAAO;QACL,MAAM;QACN,SAAS;IACX;IACA,WAAW;QACT,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;QACpC,KAAK;QACL,UAAU;YAAC;YAAM;SAAsB;IACzC;AACF,GAAG;IACD,YAAY;AACd;AAEA,UAAU;AACV,eAAe,KAAK,CAAC;IAAE,MAAM;AAAE;AAC/B,eAAe,KAAK,CAAC;IAAE,UAAU;AAAE;AACnC,eAAe,KAAK,CAAC;IAAE,OAAO;AAAE;AAEhC,uCAAuC;AACvC,eAAe,GAAG,CAAC,QAAQ,SAAS,IAAI;IACtC,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS;QAC3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAClB,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;IACzB;IACA;AACF;AAEA,oBAAoB;AACpB,eAAe,OAAO,CAAC,SAAS;IAC9B,KAAK;IACL,YAAY;IACZ,cAAc;AAChB;AAEA,uCAAuC;AACvC,eAAe,GAAG,CAAC,UAAU;IAAE,UAAU;AAAK;uCAE/B,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,QAAQ,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,YAAY", "debugId": null}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/lib/jwt.js"], "sourcesContent": ["import jwt from 'jsonwebtoken';\n\nconst JWT_SECRET = process.env.JWT_SECRET;\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';\n\nif (!JWT_SECRET) {\n  throw new Error('Please define the JWT_SECRET environment variable inside .env.local');\n}\n\nexport const signToken = (payload) => {\n  return jwt.sign(payload, JWT_SECRET, {\n    expiresIn: JWT_EXPIRES_IN,\n  });\n};\n\nexport const verifyToken = (token) => {\n  try {\n    return jwt.verify(token, JWT_SECRET);\n  } catch (error) {\n    throw new Error('Invalid token');\n  }\n};\n\nexport const generateTokens = (user) => {\n  const payload = {\n    userId: user._id,\n    email: user.email,\n    role: user.role,\n  };\n\n  const accessToken = signToken(payload);\n  \n  return {\n    accessToken,\n    user: {\n      id: user._id,\n      name: user.name,\n      email: user.email,\n      role: user.role,\n      avatar: user.avatar,\n      isVerified: user.isVerified,\n    },\n  };\n};\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU;AACzC,MAAM,iBAAiB,QAAQ,GAAG,CAAC,cAAc,IAAI;AAErD,IAAI,CAAC,YAAY;IACf,MAAM,IAAI,MAAM;AAClB;AAEO,MAAM,YAAY,CAAC;IACxB,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QACnC,WAAW;IACb;AACF;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,UAAU;QACd,QAAQ,KAAK,GAAG;QAChB,OAAO,KAAK,KAAK;QACjB,MAAM,KAAK,IAAI;IACjB;IAEA,MAAM,cAAc,UAAU;IAE9B,OAAO;QACL;QACA,MAAM;YACJ,IAAI,KAAK,GAAG;YACZ,MAAM,KAAK,IAAI;YACf,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;YACf,QAAQ,KAAK,MAAM;YACnB,YAAY,KAAK,UAAU;QAC7B;IACF;AACF", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/models/User.js"], "sourcesContent": ["import mongoose from 'mongoose';\nimport bcrypt from 'bcryptjs';\n\nconst UserSchema = new mongoose.Schema({\n  name: {\n    type: String,\n    required: [true, 'Name is required'],\n    trim: true,\n    maxlength: [50, 'Name cannot be more than 50 characters']\n  },\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    unique: true,\n    lowercase: true,\n    trim: true,\n    match: [\n      /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/,\n      'Please enter a valid email'\n    ]\n  },\n  password: {\n    type: String,\n    required: [true, 'Password is required'],\n    minlength: [6, 'Password must be at least 6 characters'],\n    select: false\n  },\n  role: {\n    type: String,\n    enum: ['user', 'admin'],\n    default: 'user'\n  },\n  avatar: {\n    type: String,\n    default: null\n  },\n  bio: {\n    type: String,\n    maxlength: [500, 'Bio cannot be more than 500 characters'],\n    default: ''\n  },\n  institution: {\n    type: String,\n    maxlength: [100, 'Institution name cannot be more than 100 characters'],\n    default: ''\n  },\n  isActive: {\n    type: Boolean,\n    default: true\n  },\n  isVerified: {\n    type: Boolean,\n    default: false\n  },\n  uploadCount: {\n    type: Number,\n    default: 0\n  },\n  downloadCount: {\n    type: Number,\n    default: 0\n  },\n  lastLogin: {\n    type: Date,\n    default: null\n  }\n}, {\n  timestamps: true\n});\n\n// Index for better query performance\nUserSchema.index({ email: 1 });\nUserSchema.index({ role: 1 });\nUserSchema.index({ isActive: 1 });\n\n// Hash password before saving\nUserSchema.pre('save', async function(next) {\n  if (!this.isModified('password')) return next();\n  \n  try {\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error);\n  }\n});\n\n// Compare password method\nUserSchema.methods.comparePassword = async function(candidatePassword) {\n  return await bcrypt.compare(candidatePassword, this.password);\n};\n\n// Get public profile\nUserSchema.methods.getPublicProfile = function() {\n  const userObject = this.toObject();\n  delete userObject.password;\n  return userObject;\n};\n\nexport default mongoose.models.User || mongoose.model('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IACrC,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAmB;QACpC,MAAM;QACN,WAAW;YAAC;YAAI;SAAyC;IAC3D;IACA,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,WAAW;QACX,MAAM;QACN,OAAO;YACL;YACA;SACD;IACH;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,WAAW;YAAC;YAAG;SAAyC;QACxD,QAAQ;IACV;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAQ;SAAQ;QACvB,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,SAAS;IACX;IACA,KAAK;QACH,MAAM;QACN,WAAW;YAAC;YAAK;SAAyC;QAC1D,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,WAAW;YAAC;YAAK;SAAsD;QACvE,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,YAAY;QACV,MAAM;QACN,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IACA,eAAe;QACb,MAAM;QACN,SAAS;IACX;IACA,WAAW;QACT,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;AACd;AAEA,qCAAqC;AACrC,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAC3B,WAAW,KAAK,CAAC;IAAE,UAAU;AAAE;AAE/B,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAe,IAAI;IACxC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,0BAA0B;AAC1B,WAAW,OAAO,CAAC,eAAe,GAAG,eAAe,iBAAiB;IACnE,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AAC9D;AAEA,qBAAqB;AACrB,WAAW,OAAO,CAAC,gBAAgB,GAAG;IACpC,MAAM,aAAa,IAAI,CAAC,QAAQ;IAChC,OAAO,WAAW,QAAQ;IAC1B,OAAO;AACT;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ", "debugId": null}}, {"offset": {"line": 436, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/middleware/auth.js"], "sourcesContent": ["import { verifyToken } from '@/lib/jwt';\nimport User from '@/models/User';\nimport connectDB from '@/lib/mongodb';\n\nexport const authenticate = async (req) => {\n  try {\n    const authHeader = req.headers.authorization;\n    \n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      throw new Error('No token provided');\n    }\n\n    const token = authHeader.substring(7);\n    const decoded = verifyToken(token);\n\n    await connectDB();\n    const user = await User.findById(decoded.userId).select('-password');\n    \n    if (!user || !user.isActive) {\n      throw new Error('User not found or inactive');\n    }\n\n    return user;\n  } catch (error) {\n    throw new Error('Authentication failed');\n  }\n};\n\nexport const requireAuth = (handler) => {\n  return async (req, res) => {\n    try {\n      const user = await authenticate(req);\n      req.user = user;\n      return handler(req, res);\n    } catch (error) {\n      return res.status(401).json({\n        success: false,\n        message: error.message || 'Authentication required'\n      });\n    }\n  };\n};\n\nexport const requireAdmin = (handler) => {\n  return async (req, res) => {\n    try {\n      const user = await authenticate(req);\n      \n      if (user.role !== 'admin') {\n        return res.status(403).json({\n          success: false,\n          message: 'Admin access required'\n        });\n      }\n\n      req.user = user;\n      return handler(req, res);\n    } catch (error) {\n      return res.status(401).json({\n        success: false,\n        message: error.message || 'Authentication required'\n      });\n    }\n  };\n};\n\nexport const optionalAuth = async (req) => {\n  try {\n    const user = await authenticate(req);\n    return user;\n  } catch (error) {\n    return null;\n  }\n};\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AAEO,MAAM,eAAe,OAAO;IACjC,IAAI;QACF,MAAM,aAAa,IAAI,OAAO,CAAC,aAAa;QAE5C,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC;QACnC,MAAM,UAAU,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE;QAE5B,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QACd,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,QAAQ,MAAM,EAAE,MAAM,CAAC;QAExD,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,MAAM,cAAc,CAAC;IAC1B,OAAO,OAAO,KAAK;QACjB,IAAI;YACF,MAAM,OAAO,MAAM,aAAa;YAChC,IAAI,IAAI,GAAG;YACX,OAAO,QAAQ,KAAK;QACtB,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS,MAAM,OAAO,IAAI;YAC5B;QACF;IACF;AACF;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,OAAO,KAAK;QACjB,IAAI;YACF,MAAM,OAAO,MAAM,aAAa;YAEhC,IAAI,KAAK,IAAI,KAAK,SAAS;gBACzB,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;oBAC1B,SAAS;oBACT,SAAS;gBACX;YACF;YAEA,IAAI,IAAI,GAAG;YACX,OAAO,QAAQ,KAAK;QACtB,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC;gBAC1B,SAAS;gBACT,SAAS,MAAM,OAAO,IAAI;YAC5B;QACF;IACF;AACF;AAEO,MAAM,eAAe,OAAO;IACjC,IAAI;QACF,MAAM,OAAO,MAAM,aAAa;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/app/api/categories/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport Category from '@/models/Category';\nimport { requireAdmin } from '@/middleware/auth';\n\nexport async function GET(request) {\n  try {\n    await connectDB();\n\n    const categories = await Category.find({ isActive: true })\n      .sort({ order: 1, name: 1 })\n      .lean();\n\n    return NextResponse.json({\n      success: true,\n      data: { categories }\n    });\n\n  } catch (error) {\n    console.error('Categories listing error:', error);\n    return NextResponse.json(\n      { success: false, message: 'Failed to fetch categories' },\n      { status: 500 }\n    );\n  }\n}\n\nasync function createCategoryHandler(request) {\n  try {\n    const { name, description, icon, color } = await request.json();\n\n    if (!name) {\n      return NextResponse.json(\n        { success: false, message: 'Category name is required' },\n        { status: 400 }\n      );\n    }\n\n    await connectDB();\n\n    // Check if category already exists\n    const existingCategory = await Category.findOne({ name });\n    if (existingCategory) {\n      return NextResponse.json(\n        { success: false, message: 'Category with this name already exists' },\n        { status: 400 }\n      );\n    }\n\n    const category = await Category.create({\n      name,\n      description: description || '',\n      icon: icon || 'folder',\n      color: color || '#3B82F6',\n      createdBy: request.user._id\n    });\n\n    return NextResponse.json({\n      success: true,\n      message: 'Category created successfully',\n      data: { category }\n    }, { status: 201 });\n\n  } catch (error) {\n    console.error('Category creation error:', error);\n    return NextResponse.json(\n      { success: false, message: 'Failed to create category' },\n      { status: 500 }\n    );\n  }\n}\n\nexport const POST = requireAdmin(createCategoryHandler);\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IAAI,OAAO;IAC/B,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,aAAa,MAAM,2HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC;YAAE,UAAU;QAAK,GACrD,IAAI,CAAC;YAAE,OAAO;YAAG,MAAM;QAAE,GACzB,IAAI;QAEP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBAAE;YAAW;QACrB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS;QAA6B,GACxD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,eAAe,sBAAsB,OAAO;IAC1C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE7D,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAA4B,GACvD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,mCAAmC;QACnC,MAAM,mBAAmB,MAAM,2HAAA,CAAA,UAAQ,CAAC,OAAO,CAAC;YAAE;QAAK;QACvD,IAAI,kBAAkB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAyC,GACpE;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;YACrC;YACA,aAAa,eAAe;YAC5B,MAAM,QAAQ;YACd,OAAO,SAAS;YAChB,WAAW,QAAQ,IAAI,CAAC,GAAG;QAC7B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM;gBAAE;YAAS;QACnB,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS;QAA4B,GACvD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,MAAM,OAAO,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,EAAE", "debugId": null}}]}
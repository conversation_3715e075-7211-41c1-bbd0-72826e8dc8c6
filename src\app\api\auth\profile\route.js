import { NextResponse } from 'next/server';
import { requireAuth } from '@/middleware/auth';

async function handler(request) {
  try {
    const user = request.user;

    return NextResponse.json({
      success: true,
      data: {
        user: user.getPublicProfile()
      }
    });

  } catch (error) {
    console.error('Profile error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export const GET = requireAuth(handler);

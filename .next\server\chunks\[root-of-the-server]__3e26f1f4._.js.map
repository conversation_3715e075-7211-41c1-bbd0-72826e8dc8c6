{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/lib/mongodb.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAEA;;;;CAIC,GACD,IAAI,SAAS,4CAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,4CAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/models/User.js"], "sourcesContent": ["import mongoose from 'mongoose';\nimport bcrypt from 'bcryptjs';\n\nconst UserSchema = new mongoose.Schema({\n  name: {\n    type: String,\n    required: [true, 'Name is required'],\n    trim: true,\n    maxlength: [50, 'Name cannot be more than 50 characters']\n  },\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    unique: true,\n    lowercase: true,\n    trim: true,\n    match: [\n      /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/,\n      'Please enter a valid email'\n    ]\n  },\n  password: {\n    type: String,\n    required: [true, 'Password is required'],\n    minlength: [6, 'Password must be at least 6 characters'],\n    select: false\n  },\n  role: {\n    type: String,\n    enum: ['user', 'admin'],\n    default: 'user'\n  },\n  avatar: {\n    type: String,\n    default: null\n  },\n  bio: {\n    type: String,\n    maxlength: [500, 'Bio cannot be more than 500 characters'],\n    default: ''\n  },\n  institution: {\n    type: String,\n    maxlength: [100, 'Institution name cannot be more than 100 characters'],\n    default: ''\n  },\n  isActive: {\n    type: Boolean,\n    default: true\n  },\n  isVerified: {\n    type: Boolean,\n    default: false\n  },\n  uploadCount: {\n    type: Number,\n    default: 0\n  },\n  downloadCount: {\n    type: Number,\n    default: 0\n  },\n  lastLogin: {\n    type: Date,\n    default: null\n  }\n}, {\n  timestamps: true\n});\n\n// Index for better query performance\nUserSchema.index({ email: 1 });\nUserSchema.index({ role: 1 });\nUserSchema.index({ isActive: 1 });\n\n// Hash password before saving\nUserSchema.pre('save', async function(next) {\n  if (!this.isModified('password')) return next();\n  \n  try {\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error);\n  }\n});\n\n// Compare password method\nUserSchema.methods.comparePassword = async function(candidatePassword) {\n  return await bcrypt.compare(candidatePassword, this.password);\n};\n\n// Get public profile\nUserSchema.methods.getPublicProfile = function() {\n  const userObject = this.toObject();\n  delete userObject.password;\n  return userObject;\n};\n\nexport default mongoose.models.User || mongoose.model('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IACrC,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAmB;QACpC,MAAM;QACN,WAAW;YAAC;YAAI;SAAyC;IAC3D;IACA,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,WAAW;QACX,MAAM;QACN,OAAO;YACL;YACA;SACD;IACH;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,WAAW;YAAC;YAAG;SAAyC;QACxD,QAAQ;IACV;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAQ;SAAQ;QACvB,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,SAAS;IACX;IACA,KAAK;QACH,MAAM;QACN,WAAW;YAAC;YAAK;SAAyC;QAC1D,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,WAAW;YAAC;YAAK;SAAsD;QACvE,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,YAAY;QACV,MAAM;QACN,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IACA,eAAe;QACb,MAAM;QACN,SAAS;IACX;IACA,WAAW;QACT,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;AACd;AAEA,qCAAqC;AACrC,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAC3B,WAAW,KAAK,CAAC;IAAE,UAAU;AAAE;AAE/B,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAe,IAAI;IACxC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,0BAA0B;AAC1B,WAAW,OAAO,CAAC,eAAe,GAAG,eAAe,iBAAiB;IACnE,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AAC9D;AAEA,qBAAqB;AACrB,WAAW,OAAO,CAAC,gBAAgB,GAAG;IACpC,MAAM,aAAa,IAAI,CAAC,QAAQ;IAChC,OAAO,WAAW,QAAQ;IAC1B,OAAO;AACT;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ", "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/lib/jwt.js"], "sourcesContent": ["import jwt from 'jsonwebtoken';\n\nconst JWT_SECRET = process.env.JWT_SECRET;\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';\n\nif (!JWT_SECRET) {\n  throw new Error('Please define the JWT_SECRET environment variable inside .env.local');\n}\n\nexport const signToken = (payload) => {\n  return jwt.sign(payload, JWT_SECRET, {\n    expiresIn: JWT_EXPIRES_IN,\n  });\n};\n\nexport const verifyToken = (token) => {\n  try {\n    return jwt.verify(token, JWT_SECRET);\n  } catch (error) {\n    throw new Error('Invalid token');\n  }\n};\n\nexport const generateTokens = (user) => {\n  const payload = {\n    userId: user._id,\n    email: user.email,\n    role: user.role,\n  };\n\n  const accessToken = signToken(payload);\n  \n  return {\n    accessToken,\n    user: {\n      id: user._id,\n      name: user.name,\n      email: user.email,\n      role: user.role,\n      avatar: user.avatar,\n      isVerified: user.isVerified,\n    },\n  };\n};\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU;AACzC,MAAM,iBAAiB,QAAQ,GAAG,CAAC,cAAc,IAAI;AAErD,IAAI,CAAC,YAAY;IACf,MAAM,IAAI,MAAM;AAClB;AAEO,MAAM,YAAY,CAAC;IACxB,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QACnC,WAAW;IACb;AACF;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,UAAU;QACd,QAAQ,KAAK,GAAG;QAChB,OAAO,KAAK,KAAK;QACjB,MAAM,KAAK,IAAI;IACjB;IAEA,MAAM,cAAc,UAAU;IAE9B,OAAO;QACL;QACA,MAAM;YACJ,IAAI,KAAK,GAAG;YACZ,MAAM,KAAK,IAAI;YACf,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;YACf,QAAQ,KAAK,MAAM;YACnB,YAAY,KAAK,UAAU;QAC7B;IACF;AACF", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/app/api/auth/register/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport User from '@/models/User';\nimport { generateTokens } from '@/lib/jwt';\n\nexport async function POST(request) {\n  try {\n    const { name, email, password, institution } = await request.json();\n\n    // Validation\n    if (!name || !email || !password) {\n      return NextResponse.json(\n        { success: false, message: 'Name, email, and password are required' },\n        { status: 400 }\n      );\n    }\n\n    if (password.length < 6) {\n      return NextResponse.json(\n        { success: false, message: 'Password must be at least 6 characters long' },\n        { status: 400 }\n      );\n    }\n\n    await connectDB();\n\n    // Check if user already exists\n    const existingUser = await User.findOne({ email });\n    if (existingUser) {\n      return NextResponse.json(\n        { success: false, message: 'User with this email already exists' },\n        { status: 400 }\n      );\n    }\n\n    // Create new user\n    const user = await User.create({\n      name,\n      email,\n      password,\n      institution: institution || '',\n    });\n\n    // Generate tokens\n    const tokens = generateTokens(user);\n\n    return NextResponse.json({\n      success: true,\n      message: 'User registered successfully',\n      data: tokens\n    }, { status: 201 });\n\n  } catch (error) {\n    console.error('Registration error:', error);\n    return NextResponse.json(\n      { success: false, message: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,KAAK,OAAO;IAChC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEjE,aAAa;QACb,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAyC,GACpE;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAA8C,GACzE;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,+BAA+B;QAC/B,MAAM,eAAe,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE;QAAM;QAChD,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAsC,GACjE;gBAAE,QAAQ;YAAI;QAElB;QAEA,kBAAkB;QAClB,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,MAAM,CAAC;YAC7B;YACA;YACA;YACA,aAAa,eAAe;QAC9B;QAEA,kBAAkB;QAClB,MAAM,SAAS,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;QAE9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM;QACR,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS;QAAwB,GACnD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
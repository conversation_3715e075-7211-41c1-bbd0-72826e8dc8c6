import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Comment from '@/models/Comment';
import File from '@/models/File';
import { requireAuth } from '@/middleware/auth';

async function createCommentHandler(request) {
  try {
    const { content, fileId, parentCommentId } = await request.json();

    if (!content || !fileId) {
      return NextResponse.json(
        { success: false, message: 'Content and file ID are required' },
        { status: 400 }
      );
    }

    if (content.trim().length === 0) {
      return NextResponse.json(
        { success: false, message: 'Comment content cannot be empty' },
        { status: 400 }
      );
    }

    await connectDB();

    // Check if file exists and is accessible
    const file = await File.findById(fileId);
    if (!file) {
      return NextResponse.json(
        { success: false, message: 'File not found' },
        { status: 404 }
      );
    }

    if (file.status !== 'approved' || !file.isPublic) {
      return NextResponse.json(
        { success: false, message: 'Cannot comment on this file' },
        { status: 403 }
      );
    }

    // If replying to a comment, check if parent comment exists
    if (parentCommentId) {
      const parentComment = await Comment.findById(parentCommentId);
      if (!parentComment || parentComment.file.toString() !== fileId) {
        return NextResponse.json(
          { success: false, message: 'Invalid parent comment' },
          { status: 400 }
        );
      }
    }

    // Create comment
    const comment = await Comment.create({
      content: content.trim(),
      author: request.user._id,
      file: fileId,
      parentComment: parentCommentId || null
    });

    // Populate author information
    await comment.populate('author', 'name avatar');

    return NextResponse.json({
      success: true,
      message: 'Comment added successfully',
      data: { comment }
    }, { status: 201 });

  } catch (error) {
    console.error('Comment creation error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to add comment' },
      { status: 500 }
    );
  }
}

export const POST = requireAuth(createCommentHandler);

{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/contexts/AuthContext.js"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useReducer, useEffect } from 'react';\n\nconst AuthContext = createContext();\n\nconst initialState = {\n  user: null,\n  token: null,\n  isLoading: true,\n  isAuthenticated: false,\n};\n\nfunction authReducer(state, action) {\n  switch (action.type) {\n    case 'LOGIN_SUCCESS':\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.accessToken,\n        isAuthenticated: true,\n        isLoading: false,\n      };\n    case 'LOGOUT':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n      };\n    case 'SET_LOADING':\n      return {\n        ...state,\n        isLoading: action.payload,\n      };\n    case 'UPDATE_USER':\n      return {\n        ...state,\n        user: { ...state.user, ...action.payload },\n      };\n    default:\n      return state;\n  }\n}\n\nexport function AuthProvider({ children }) {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  useEffect(() => {\n    // Check for stored token on mount\n    const token = localStorage.getItem('token');\n    const user = localStorage.getItem('user');\n\n    if (token && user) {\n      try {\n        const parsedUser = JSON.parse(user);\n        dispatch({\n          type: 'LOGIN_SUCCESS',\n          payload: {\n            accessToken: token,\n            user: parsedUser,\n          },\n        });\n      } catch (error) {\n        console.error('Error parsing stored user data:', error);\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n      }\n    }\n\n    dispatch({ type: 'SET_LOADING', payload: false });\n  }, []);\n\n  const login = (data) => {\n    localStorage.setItem('token', data.accessToken);\n    localStorage.setItem('user', JSON.stringify(data.user));\n    dispatch({ type: 'LOGIN_SUCCESS', payload: data });\n  };\n\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    dispatch({ type: 'LOGOUT' });\n  };\n\n  const updateUser = (userData) => {\n    const updatedUser = { ...state.user, ...userData };\n    localStorage.setItem('user', JSON.stringify(updatedUser));\n    dispatch({ type: 'UPDATE_USER', payload: userData });\n  };\n\n  const value = {\n    ...state,\n    login,\n    logout,\n    updateUser,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAIA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;AAEhC,MAAM,eAAe;IACnB,MAAM;IACN,OAAO;IACP,WAAW;IACX,iBAAiB;AACnB;AAEA,SAAS,YAAY,KAAK,EAAE,MAAM;IAChC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM,OAAO,OAAO,CAAC,IAAI;gBACzB,OAAO,OAAO,OAAO,CAAC,WAAW;gBACjC,iBAAiB;gBACjB,WAAW;YACb;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM;gBACN,OAAO;gBACP,iBAAiB;gBACjB,WAAW;YACb;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,WAAW,OAAO,OAAO;YAC3B;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM;oBAAE,GAAG,MAAM,IAAI;oBAAE,GAAG,OAAO,OAAO;gBAAC;YAC3C;QACF;YACE,OAAO;IACX;AACF;AAEO,SAAS,aAAa,EAAE,QAAQ,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,aAAa;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kCAAkC;QAClC,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,MAAM,OAAO,aAAa,OAAO,CAAC;QAElC,IAAI,SAAS,MAAM;YACjB,IAAI;gBACF,MAAM,aAAa,KAAK,KAAK,CAAC;gBAC9B,SAAS;oBACP,MAAM;oBACN,SAAS;wBACP,aAAa;wBACb,MAAM;oBACR;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;YAC1B;QACF;QAEA,SAAS;YAAE,MAAM;YAAe,SAAS;QAAM;IACjD,GAAG,EAAE;IAEL,MAAM,QAAQ,CAAC;QACb,aAAa,OAAO,CAAC,SAAS,KAAK,WAAW;QAC9C,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,KAAK,IAAI;QACrD,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAK;IAClD;IAEA,MAAM,SAAS;QACb,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,SAAS;YAAE,MAAM;QAAS;IAC5B;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc;YAAE,GAAG,MAAM,IAAI;YAAE,GAAG,QAAQ;QAAC;QACjD,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;QAC5C,SAAS;YAAE,MAAM;YAAe,SAAS;QAAS;IACpD;IAEA,MAAM,QAAQ;QACZ,GAAG,KAAK;QACR;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/components/Navigation.js"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { \n  BookOpenIcon, \n  UserIcon, \n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon,\n  Bars3Icon,\n  XMarkIcon,\n  MagnifyingGlassIcon,\n  PlusIcon\n} from '@heroicons/react/24/outline';\n\nexport default function Navigation() {\n  const { user, isAuthenticated, logout } = useAuth();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  const handleLogout = () => {\n    logout();\n    setIsMobileMenuOpen(false);\n  };\n\n  const navigation = [\n    { name: 'Browse Files', href: '/files', icon: BookOpenIcon },\n    { name: 'Categories', href: '/categories', icon: BookOpenIcon },\n  ];\n\n  const userNavigation = [\n    { name: 'My Profile', href: '/profile', icon: UserIcon },\n    { name: 'My Uploads', href: '/my-uploads', icon: BookOpenIcon },\n    { name: 'Upload File', href: '/upload', icon: PlusIcon },\n  ];\n\n  const adminNavigation = [\n    { name: 'Admin Panel', href: '/admin', icon: Cog6ToothIcon },\n  ];\n\n  return (\n    <nav className=\"bg-white shadow-lg border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* Logo and main navigation */}\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0 flex items-center\">\n              <Link href=\"/\" className=\"text-2xl font-bold text-blue-600\">\n                NOTEX\n              </Link>\n            </div>\n            <div className=\"hidden sm:ml-6 sm:flex sm:space-x-8\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\n                >\n                  <item.icon className=\"h-4 w-4 mr-2\" />\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Search bar */}\n          <div className=\"hidden sm:flex sm:items-center sm:flex-1 sm:max-w-xs sm:mx-4\">\n            <div className=\"relative w-full\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search files...\"\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              />\n            </div>\n          </div>\n\n          {/* User menu */}\n          <div className=\"hidden sm:ml-6 sm:flex sm:items-center\">\n            {isAuthenticated ? (\n              <div className=\"flex items-center space-x-4\">\n                {userNavigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium flex items-center\"\n                  >\n                    <item.icon className=\"h-4 w-4 mr-1\" />\n                    {item.name}\n                  </Link>\n                ))}\n                \n                {user?.role === 'admin' && adminNavigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium flex items-center\"\n                  >\n                    <item.icon className=\"h-4 w-4 mr-1\" />\n                    {item.name}\n                  </Link>\n                ))}\n\n                <button\n                  onClick={handleLogout}\n                  className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium flex items-center\"\n                >\n                  <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-1\" />\n                  Logout\n                </button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-4\">\n                <Link\n                  href=\"/login\"\n                  className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Login\n                </Link>\n                <Link\n                  href=\"/register\"\n                  className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n                >\n                  Register\n                </Link>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"sm:hidden flex items-center\">\n            <button\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n            >\n              {isMobileMenuOpen ? (\n                <XMarkIcon className=\"block h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"block h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isMobileMenuOpen && (\n        <div className=\"sm:hidden\">\n          <div className=\"pt-2 pb-3 space-y-1\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium\"\n                onClick={() => setIsMobileMenuOpen(false)}\n              >\n                <div className=\"flex items-center\">\n                  <item.icon className=\"h-5 w-5 mr-3\" />\n                  {item.name}\n                </div>\n              </Link>\n            ))}\n          </div>\n          \n          {isAuthenticated && (\n            <div className=\"pt-4 pb-3 border-t border-gray-200\">\n              <div className=\"space-y-1\">\n                {userNavigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium\"\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    <div className=\"flex items-center\">\n                      <item.icon className=\"h-5 w-5 mr-3\" />\n                      {item.name}\n                    </div>\n                  </Link>\n                ))}\n                \n                {user?.role === 'admin' && adminNavigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium\"\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    <div className=\"flex items-center\">\n                      <item.icon className=\"h-5 w-5 mr-3\" />\n                      {item.name}\n                    </div>\n                  </Link>\n                ))}\n\n                <button\n                  onClick={handleLogout}\n                  className=\"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium w-full text-left\"\n                >\n                  <div className=\"flex items-center\">\n                    <ArrowRightOnRectangleIcon className=\"h-5 w-5 mr-3\" />\n                    Logout\n                  </div>\n                </button>\n              </div>\n            </div>\n          )}\n\n          {!isAuthenticated && (\n            <div className=\"pt-4 pb-3 border-t border-gray-200\">\n              <div className=\"space-y-1\">\n                <Link\n                  href=\"/login\"\n                  className=\"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium\"\n                  onClick={() => setIsMobileMenuOpen(false)}\n                >\n                  Login\n                </Link>\n                <Link\n                  href=\"/register\"\n                  className=\"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium\"\n                  onClick={() => setIsMobileMenuOpen(false)}\n                >\n                  Register\n                </Link>\n              </div>\n            </div>\n          )}\n        </div>\n      )}\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAgBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAChD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,eAAe;QACnB;QACA,oBAAoB;IACtB;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAgB,MAAM;YAAU,MAAM,uNAAA,CAAA,eAAY;QAAC;QAC3D;YAAE,MAAM;YAAc,MAAM;YAAe,MAAM,uNAAA,CAAA,eAAY;QAAC;KAC/D;IAED,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAAc,MAAM;YAAY,MAAM,+MAAA,CAAA,WAAQ;QAAC;QACvD;YAAE,MAAM;YAAc,MAAM;YAAe,MAAM,uNAAA,CAAA,eAAY;QAAC;QAC9D;YAAE,MAAM;YAAe,MAAM;YAAW,MAAM,+MAAA,CAAA,WAAQ;QAAC;KACxD;IAED,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAe,MAAM;YAAU,MAAM,yNAAA,CAAA,gBAAa;QAAC;KAC5D;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAmC;;;;;;;;;;;8CAI9D,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;;8DAEV,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAYtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;;;;;;kDAEjC,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAU;sCACZ,gCACC,8OAAC;gCAAI,WAAU;;oCACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;;8DAEV,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;2CALL,KAAK,IAAI;;;;;oCASjB,MAAM,SAAS,WAAW,gBAAgB,GAAG,CAAC,CAAC,qBAC9C,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;;8DAEV,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;2CALL,KAAK,IAAI;;;;;kDASlB,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,iPAAA,CAAA,4BAAyB;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;qDAK1D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAQP,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,oBAAoB,CAAC;gCACpC,WAAU;0CAET,iCACC,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;yDAErB,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ9B,kCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,oBAAoB;0CAEnC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;;;;;;+BAPP,KAAK,IAAI;;;;;;;;;;oBAanB,iCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;wCACV,SAAS,IAAM,oBAAoB;kDAEnC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;;;;;;uCAPP,KAAK,IAAI;;;;;gCAYjB,MAAM,SAAS,WAAW,gBAAgB,GAAG,CAAC,CAAC,qBAC9C,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;wCACV,SAAS,IAAM,oBAAoB;kDAEnC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;;;;;;uCAPP,KAAK,IAAI;;;;;8CAYlB,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iPAAA,CAAA,4BAAyB;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;oBAQ/D,CAAC,iCACA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CACpC;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CACpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 645, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/components/FileCard.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { formatDistanceToNow } from 'date-fns';\nimport { \n  EyeIcon, \n  ArrowDownTrayIcon, \n  StarIcon,\n  DocumentIcon,\n  PhotoIcon\n} from '@heroicons/react/24/outline';\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';\n\nexport default function FileCard({ file }) {\n  const getFileIcon = (fileType) => {\n    const imageTypes = ['jpg', 'jpeg', 'png', 'gif'];\n    if (imageTypes.includes(fileType.toLowerCase())) {\n      return <PhotoIcon className=\"h-8 w-8 text-blue-500\" />;\n    }\n    return <DocumentIcon className=\"h-8 w-8 text-gray-500\" />;\n  };\n\n  const formatFileSize = (bytes) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const renderStars = (rating) => {\n    const stars = [];\n    const fullStars = Math.floor(rating);\n    const hasHalfStar = rating % 1 !== 0;\n\n    for (let i = 0; i < 5; i++) {\n      if (i < fullStars) {\n        stars.push(\n          <StarIconSolid key={i} className=\"h-4 w-4 text-yellow-400\" />\n        );\n      } else if (i === fullStars && hasHalfStar) {\n        stars.push(\n          <div key={i} className=\"relative\">\n            <StarIcon className=\"h-4 w-4 text-gray-300\" />\n            <StarIconSolid className=\"h-4 w-4 text-yellow-400 absolute top-0 left-0 w-1/2 overflow-hidden\" />\n          </div>\n        );\n      } else {\n        stars.push(\n          <StarIcon key={i} className=\"h-4 w-4 text-gray-300\" />\n        );\n      }\n    }\n\n    return stars;\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden\">\n      {/* File preview/thumbnail */}\n      <div className=\"h-48 bg-gray-50 flex items-center justify-center\">\n        {file.thumbnailUrl ? (\n          <img\n            src={file.thumbnailUrl}\n            alt={file.title}\n            className=\"h-full w-full object-cover\"\n          />\n        ) : (\n          <div className=\"flex flex-col items-center justify-center text-gray-400\">\n            {getFileIcon(file.fileType)}\n            <span className=\"mt-2 text-sm font-medium uppercase\">\n              {file.fileType}\n            </span>\n          </div>\n        )}\n      </div>\n\n      {/* File information */}\n      <div className=\"p-4\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex-1 min-w-0\">\n            <Link href={`/files/${file._id}`}>\n              <h3 className=\"text-lg font-semibold text-gray-900 hover:text-blue-600 cursor-pointer truncate\">\n                {file.title}\n              </h3>\n            </Link>\n            <p className=\"text-sm text-gray-600 mt-1 line-clamp-2\">\n              {file.description}\n            </p>\n          </div>\n        </div>\n\n        {/* File metadata */}\n        <div className=\"mt-3 space-y-2\">\n          <div className=\"flex items-center justify-between text-sm text-gray-500\">\n            <span className=\"font-medium\">{file.subject}</span>\n            <span>{formatFileSize(file.fileSize)}</span>\n          </div>\n          \n          <div className=\"flex items-center justify-between text-sm text-gray-500\">\n            <span>{file.course}</span>\n            <span className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs\">\n              {file.category?.name}\n            </span>\n          </div>\n\n          {/* Author and upload date */}\n          <div className=\"flex items-center justify-between text-sm text-gray-500\">\n            <span>by {file.uploadedBy?.name}</span>\n            <span>{formatDistanceToNow(new Date(file.createdAt), { addSuffix: true })}</span>\n          </div>\n        </div>\n\n        {/* Rating */}\n        {file.rating && file.rating.count > 0 && (\n          <div className=\"mt-3 flex items-center\">\n            <div className=\"flex items-center\">\n              {renderStars(file.rating.average)}\n            </div>\n            <span className=\"ml-2 text-sm text-gray-600\">\n              {file.rating.average.toFixed(1)} ({file.rating.count} reviews)\n            </span>\n          </div>\n        )}\n\n        {/* Tags */}\n        {file.tags && file.tags.length > 0 && (\n          <div className=\"mt-3\">\n            <div className=\"flex flex-wrap gap-1\">\n              {file.tags.slice(0, 3).map((tag, index) => (\n                <span\n                  key={index}\n                  className=\"inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded\"\n                >\n                  {tag}\n                </span>\n              ))}\n              {file.tags.length > 3 && (\n                <span className=\"inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded\">\n                  +{file.tags.length - 3} more\n                </span>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Stats and actions */}\n        <div className=\"mt-4 flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n            <div className=\"flex items-center\">\n              <EyeIcon className=\"h-4 w-4 mr-1\" />\n              {file.viewCount || 0}\n            </div>\n            <div className=\"flex items-center\">\n              <ArrowDownTrayIcon className=\"h-4 w-4 mr-1\" />\n              {file.downloadCount || 0}\n            </div>\n          </div>\n\n          <Link\n            href={`/files/${file._id}`}\n            className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\n          >\n            View Details\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAXA;;;;;;AAae,SAAS,SAAS,EAAE,IAAI,EAAE;IACvC,MAAM,cAAc,CAAC;QACnB,MAAM,aAAa;YAAC;YAAO;YAAQ;YAAO;SAAM;QAChD,IAAI,WAAW,QAAQ,CAAC,SAAS,WAAW,KAAK;YAC/C,qBAAO,8OAAC,iNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAC9B;QACA,qBAAO,8OAAC,uNAAA,CAAA,eAAY;YAAC,WAAU;;;;;;IACjC;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAQ,EAAE;QAChB,MAAM,YAAY,KAAK,KAAK,CAAC;QAC7B,MAAM,cAAc,SAAS,MAAM;QAEnC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,IAAI,IAAI,WAAW;gBACjB,MAAM,IAAI,eACR,8OAAC,6MAAA,CAAA,WAAa;oBAAS,WAAU;mBAAb;;;;;YAExB,OAAO,IAAI,MAAM,aAAa,aAAa;gBACzC,MAAM,IAAI,eACR,8OAAC;oBAAY,WAAU;;sCACrB,8OAAC,+MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC,6MAAA,CAAA,WAAa;4BAAC,WAAU;;;;;;;mBAFjB;;;;;YAKd,OAAO;gBACL,MAAM,IAAI,eACR,8OAAC,+MAAA,CAAA,WAAQ;oBAAS,WAAU;mBAAb;;;;;YAEnB;QACF;QAEA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACZ,KAAK,YAAY,iBAChB,8OAAC;oBACC,KAAK,KAAK,YAAY;oBACtB,KAAK,KAAK,KAAK;oBACf,WAAU;;;;;yCAGZ,8OAAC;oBAAI,WAAU;;wBACZ,YAAY,KAAK,QAAQ;sCAC1B,8OAAC;4BAAK,WAAU;sCACb,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAOtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;8CAC9B,cAAA,8OAAC;wCAAG,WAAU;kDACX,KAAK,KAAK;;;;;;;;;;;8CAGf,8OAAC;oCAAE,WAAU;8CACV,KAAK,WAAW;;;;;;;;;;;;;;;;;kCAMvB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAe,KAAK,OAAO;;;;;;kDAC3C,8OAAC;kDAAM,eAAe,KAAK,QAAQ;;;;;;;;;;;;0CAGrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAM,KAAK,MAAM;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDACb,KAAK,QAAQ,EAAE;;;;;;;;;;;;0CAKpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAK;4CAAI,KAAK,UAAU,EAAE;;;;;;;kDAC3B,8OAAC;kDAAM,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,KAAK,SAAS,GAAG;4CAAE,WAAW;wCAAK;;;;;;;;;;;;;;;;;;oBAK1E,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,KAAK,GAAG,mBAClC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,YAAY,KAAK,MAAM,CAAC,OAAO;;;;;;0CAElC,8OAAC;gCAAK,WAAU;;oCACb,KAAK,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;oCAAG;oCAAG,KAAK,MAAM,CAAC,KAAK;oCAAC;;;;;;;;;;;;;oBAM1D,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;gCAMR,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,8OAAC;oCAAK,WAAU;;wCAAmE;wCAC/E,KAAK,IAAI,CAAC,MAAM,GAAG;wCAAE;;;;;;;;;;;;;;;;;;kCAQjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAClB,KAAK,SAAS,IAAI;;;;;;;kDAErB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iOAAA,CAAA,oBAAiB;gDAAC,WAAU;;;;;;4CAC5B,KAAK,aAAa,IAAI;;;;;;;;;;;;;0CAI3B,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;gCAC1B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 1056, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/components/Loading.js"], "sourcesContent": ["export default function Loading({ size = 'md', text = 'Loading...' }) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12',\n    xl: 'h-16 w-16'\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center p-8\">\n      <div className={`animate-spin rounded-full border-b-2 border-blue-600 ${sizeClasses[size]}`}></div>\n      {text && (\n        <p className=\"mt-4 text-gray-600 text-sm\">{text}</p>\n      )}\n    </div>\n  );\n}\n\nexport function LoadingCard() {\n  return (\n    <div className=\"bg-white rounded-lg shadow-md overflow-hidden animate-pulse\">\n      <div className=\"h-48 bg-gray-200\"></div>\n      <div className=\"p-4\">\n        <div className=\"h-6 bg-gray-200 rounded mb-2\"></div>\n        <div className=\"h-4 bg-gray-200 rounded mb-4\"></div>\n        <div className=\"space-y-2\">\n          <div className=\"h-3 bg-gray-200 rounded\"></div>\n          <div className=\"h-3 bg-gray-200 rounded\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-3/4\"></div>\n        </div>\n        <div className=\"mt-4 flex justify-between\">\n          <div className=\"h-4 bg-gray-200 rounded w-1/3\"></div>\n          <div className=\"h-8 bg-gray-200 rounded w-1/4\"></div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport function LoadingGrid({ count = 6 }) {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n      {Array.from({ length: count }).map((_, index) => (\n        <LoadingCard key={index} />\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAAe,SAAS,QAAQ,EAAE,OAAO,IAAI,EAAE,OAAO,YAAY,EAAE;IAClE,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAW,CAAC,qDAAqD,EAAE,WAAW,CAAC,KAAK,EAAE;;;;;;YAC1F,sBACC,8OAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKzB;AAEO,SAAS,YAAY,EAAE,QAAQ,CAAC,EAAE;IACvC,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,8OAAC,iBAAiB;;;;;;;;;;AAI1B", "debugId": null}}, {"offset": {"line": 1209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/notex/src/app/files/page.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useSearchParams } from 'next/navigation';\nimport { AuthProvider } from '@/contexts/AuthContext';\nimport Navigation from '@/components/Navigation';\nimport FileCard from '@/components/FileCard';\nimport { LoadingGrid } from '@/components/Loading';\nimport { \n  MagnifyingGlassIcon, \n  FunnelIcon,\n  ChevronLeftIcon,\n  ChevronRightIcon\n} from '@heroicons/react/24/outline';\n\nfunction FilesPage() {\n  const [files, setFiles] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [pagination, setPagination] = useState({});\n  const [filters, setFilters] = useState({\n    search: '',\n    category: '',\n    subject: '',\n    fileType: '',\n    sortBy: 'createdAt',\n    sortOrder: 'desc'\n  });\n  const [showFilters, setShowFilters] = useState(false);\n\n  const searchParams = useSearchParams();\n\n  useEffect(() => {\n    // Initialize filters from URL params\n    const initialFilters = {\n      search: searchParams.get('search') || '',\n      category: searchParams.get('category') || '',\n      subject: searchParams.get('subject') || '',\n      fileType: searchParams.get('fileType') || '',\n      sortBy: searchParams.get('sortBy') || 'createdAt',\n      sortOrder: searchParams.get('sortOrder') || 'desc'\n    };\n    setFilters(initialFilters);\n    \n    fetchCategories();\n    fetchFiles(1, initialFilters);\n  }, [searchParams]);\n\n  const fetchCategories = async () => {\n    try {\n      const response = await fetch('/api/categories');\n      if (response.ok) {\n        const data = await response.json();\n        setCategories(data.data.categories);\n      }\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n    }\n  };\n\n  const fetchFiles = async (page = 1, currentFilters = filters) => {\n    setIsLoading(true);\n    try {\n      const params = new URLSearchParams({\n        page: page.toString(),\n        limit: '12',\n        ...currentFilters\n      });\n\n      // Remove empty values\n      Object.keys(currentFilters).forEach(key => {\n        if (!currentFilters[key]) {\n          params.delete(key);\n        }\n      });\n\n      const response = await fetch(`/api/files?${params}`);\n      if (response.ok) {\n        const data = await response.json();\n        setFiles(data.data.files);\n        setPagination(data.data.pagination);\n      }\n    } catch (error) {\n      console.error('Error fetching files:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleFilterChange = (key, value) => {\n    const newFilters = { ...filters, [key]: value };\n    setFilters(newFilters);\n    fetchFiles(1, newFilters);\n  };\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    fetchFiles(1, filters);\n  };\n\n  const handlePageChange = (page) => {\n    fetchFiles(page);\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  const clearFilters = () => {\n    const clearedFilters = {\n      search: '',\n      category: '',\n      subject: '',\n      fileType: '',\n      sortBy: 'createdAt',\n      sortOrder: 'desc'\n    };\n    setFilters(clearedFilters);\n    fetchFiles(1, clearedFilters);\n  };\n\n  const fileTypes = ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'txt', 'jpg', 'jpeg', 'png', 'gif'];\n  const sortOptions = [\n    { value: 'createdAt', label: 'Date Added' },\n    { value: 'title', label: 'Title' },\n    { value: 'downloadCount', label: 'Downloads' },\n    { value: 'rating.average', label: 'Rating' },\n    { value: 'viewCount', label: 'Views' }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation />\n      \n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">Browse Files</h1>\n          <p className=\"text-gray-600\">\n            Discover educational resources shared by our community\n          </p>\n        </div>\n\n        {/* Search and Filters */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8\">\n          {/* Search Bar */}\n          <form onSubmit={handleSearch} className=\"mb-4\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search files, subjects, courses...\"\n                value={filters.search}\n                onChange={(e) => setFilters({ ...filters, search: e.target.value })}\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n              />\n            </div>\n          </form>\n\n          {/* Filter Toggle */}\n          <div className=\"flex items-center justify-between\">\n            <button\n              onClick={() => setShowFilters(!showFilters)}\n              className=\"flex items-center text-gray-600 hover:text-gray-900\"\n            >\n              <FunnelIcon className=\"h-5 w-5 mr-2\" />\n              Filters\n            </button>\n            \n            {(filters.category || filters.subject || filters.fileType) && (\n              <button\n                onClick={clearFilters}\n                className=\"text-blue-600 hover:text-blue-700 text-sm\"\n              >\n                Clear Filters\n              </button>\n            )}\n          </div>\n\n          {/* Filters */}\n          {showFilters && (\n            <div className=\"mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Category\n                </label>\n                <select\n                  value={filters.category}\n                  onChange={(e) => handleFilterChange('category', e.target.value)}\n                  className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"\">All Categories</option>\n                  {categories.map((category) => (\n                    <option key={category._id} value={category._id}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  File Type\n                </label>\n                <select\n                  value={filters.fileType}\n                  onChange={(e) => handleFilterChange('fileType', e.target.value)}\n                  className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"\">All Types</option>\n                  {fileTypes.map((type) => (\n                    <option key={type} value={type}>\n                      {type.toUpperCase()}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Sort By\n                </label>\n                <select\n                  value={filters.sortBy}\n                  onChange={(e) => handleFilterChange('sortBy', e.target.value)}\n                  className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  {sortOptions.map((option) => (\n                    <option key={option.value} value={option.value}>\n                      {option.label}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Order\n                </label>\n                <select\n                  value={filters.sortOrder}\n                  onChange={(e) => handleFilterChange('sortOrder', e.target.value)}\n                  className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"desc\">Descending</option>\n                  <option value=\"asc\">Ascending</option>\n                </select>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Results */}\n        <div className=\"mb-8\">\n          {pagination.totalFiles !== undefined && (\n            <p className=\"text-gray-600 mb-4\">\n              Showing {files.length} of {pagination.totalFiles} files\n            </p>\n          )}\n\n          {isLoading ? (\n            <LoadingGrid count={12} />\n          ) : files.length > 0 ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {files.map((file) => (\n                <FileCard key={file._id} file={file} />\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-12\">\n              <MagnifyingGlassIcon className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                No files found\n              </h3>\n              <p className=\"text-gray-600\">\n                Try adjusting your search criteria or filters\n              </p>\n            </div>\n          )}\n        </div>\n\n        {/* Pagination */}\n        {pagination.totalPages > 1 && (\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <p className=\"text-sm text-gray-700\">\n                Page {pagination.currentPage} of {pagination.totalPages}\n              </p>\n            </div>\n            \n            <div className=\"flex items-center space-x-2\">\n              <button\n                onClick={() => handlePageChange(pagination.currentPage - 1)}\n                disabled={!pagination.hasPrevPage}\n                className=\"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                <ChevronLeftIcon className=\"h-5 w-5\" />\n              </button>\n              \n              {/* Page numbers */}\n              {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {\n                const pageNum = Math.max(1, pagination.currentPage - 2) + i;\n                if (pageNum <= pagination.totalPages) {\n                  return (\n                    <button\n                      key={pageNum}\n                      onClick={() => handlePageChange(pageNum)}\n                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${\n                        pageNum === pagination.currentPage\n                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'\n                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'\n                      }`}\n                    >\n                      {pageNum}\n                    </button>\n                  );\n                }\n                return null;\n              })}\n              \n              <button\n                onClick={() => handlePageChange(pagination.currentPage + 1)}\n                disabled={!pagination.hasNextPage}\n                className=\"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                <ChevronRightIcon className=\"h-5 w-5\" />\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\nexport default function Files() {\n  return (\n    <AuthProvider>\n      <FilesPage />\n    </AuthProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAeA,SAAS;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,QAAQ;QACR,UAAU;QACV,SAAS;QACT,UAAU;QACV,QAAQ;QACR,WAAW;IACb;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qCAAqC;QACrC,MAAM,iBAAiB;YACrB,QAAQ,aAAa,GAAG,CAAC,aAAa;YACtC,UAAU,aAAa,GAAG,CAAC,eAAe;YAC1C,SAAS,aAAa,GAAG,CAAC,cAAc;YACxC,UAAU,aAAa,GAAG,CAAC,eAAe;YAC1C,QAAQ,aAAa,GAAG,CAAC,aAAa;YACtC,WAAW,aAAa,GAAG,CAAC,gBAAgB;QAC9C;QACA,WAAW;QAEX;QACA,WAAW,GAAG;IAChB,GAAG;QAAC;KAAa;IAEjB,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,cAAc,KAAK,IAAI,CAAC,UAAU;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,aAAa,OAAO,OAAO,CAAC,EAAE,iBAAiB,OAAO;QAC1D,aAAa;QACb,IAAI;YACF,MAAM,SAAS,IAAI,gBAAgB;gBACjC,MAAM,KAAK,QAAQ;gBACnB,OAAO;gBACP,GAAG,cAAc;YACnB;YAEA,sBAAsB;YACtB,OAAO,IAAI,CAAC,gBAAgB,OAAO,CAAC,CAAA;gBAClC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;oBACxB,OAAO,MAAM,CAAC;gBAChB;YACF;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,QAAQ;YACnD,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,IAAI,CAAC,KAAK;gBACxB,cAAc,KAAK,IAAI,CAAC,UAAU;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB,CAAC,KAAK;QAC/B,MAAM,aAAa;YAAE,GAAG,OAAO;YAAE,CAAC,IAAI,EAAE;QAAM;QAC9C,WAAW;QACX,WAAW,GAAG;IAChB;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,WAAW,GAAG;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,WAAW;QACX,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,MAAM,eAAe;QACnB,MAAM,iBAAiB;YACrB,QAAQ;YACR,UAAU;YACV,SAAS;YACT,UAAU;YACV,QAAQ;YACR,WAAW;QACb;QACA,WAAW;QACX,WAAW,GAAG;IAChB;IAEA,MAAM,YAAY;QAAC;QAAO;QAAO;QAAQ;QAAO;QAAQ;QAAO;QAAO;QAAQ;QAAO;KAAM;IAC3F,MAAM,cAAc;QAClB;YAAE,OAAO;YAAa,OAAO;QAAa;QAC1C;YAAE,OAAO;YAAS,OAAO;QAAQ;QACjC;YAAE,OAAO;YAAiB,OAAO;QAAY;QAC7C;YAAE,OAAO;YAAkB,OAAO;QAAS;QAC3C;YAAE,OAAO;YAAa,OAAO;QAAQ;KACtC;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,+HAAA,CAAA,UAAU;;;;;0BAEX,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAM/B,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAK,UAAU;gCAAc,WAAU;0CACtC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;gDAAC,WAAU;;;;;;;;;;;sDAEjC,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO,QAAQ,MAAM;4CACrB,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACjE,WAAU;;;;;;;;;;;;;;;;;0CAMhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAU;;0DAEV,8OAAC,mNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;oCAIxC,CAAC,QAAQ,QAAQ,IAAI,QAAQ,OAAO,IAAI,QAAQ,QAAQ,mBACvD,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;4BAOJ,6BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,QAAQ,QAAQ;gDACvB,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC9D,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;4DAA0B,OAAO,SAAS,GAAG;sEAC3C,SAAS,IAAI;2DADH,SAAS,GAAG;;;;;;;;;;;;;;;;;kDAO/B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,QAAQ,QAAQ;gDACvB,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC9D,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;4DAAkB,OAAO;sEACvB,KAAK,WAAW;2DADN;;;;;;;;;;;;;;;;;kDAOnB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,QAAQ,MAAM;gDACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;gDAC5D,WAAU;0DAET,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC;wDAA0B,OAAO,OAAO,KAAK;kEAC3C,OAAO,KAAK;uDADF,OAAO,KAAK;;;;;;;;;;;;;;;;kDAO/B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,QAAQ,SAAS;gDACxB,UAAU,CAAC,IAAM,mBAAmB,aAAa,EAAE,MAAM,CAAC,KAAK;gDAC/D,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,8OAAC;wDAAO,OAAM;kEAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ9B,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,UAAU,KAAK,2BACzB,8OAAC;gCAAE,WAAU;;oCAAqB;oCACvB,MAAM,MAAM;oCAAC;oCAAK,WAAW,UAAU;oCAAC;;;;;;;4BAIpD,0BACC,8OAAC,4HAAA,CAAA,cAAW;gCAAC,OAAO;;;;;uCAClB,MAAM,MAAM,GAAG,kBACjB,8OAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,6HAAA,CAAA,UAAQ;wCAAgB,MAAM;uCAAhB,KAAK,GAAG;;;;;;;;;qDAI3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,qOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;kDAC/B,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;oBAQlC,WAAW,UAAU,GAAG,mBACvB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;wCAAwB;wCAC7B,WAAW,WAAW;wCAAC;wCAAK,WAAW,UAAU;;;;;;;;;;;;0CAI3D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,iBAAiB,WAAW,WAAW,GAAG;wCACzD,UAAU,CAAC,WAAW,WAAW;wCACjC,WAAU;kDAEV,cAAA,8OAAC,6NAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;;;;;;oCAI5B,MAAM,IAAI,CAAC;wCAAE,QAAQ,KAAK,GAAG,CAAC,GAAG,WAAW,UAAU;oCAAE,GAAG,CAAC,GAAG;wCAC9D,MAAM,UAAU,KAAK,GAAG,CAAC,GAAG,WAAW,WAAW,GAAG,KAAK;wCAC1D,IAAI,WAAW,WAAW,UAAU,EAAE;4CACpC,qBACE,8OAAC;gDAEC,SAAS,IAAM,iBAAiB;gDAChC,WAAW,CAAC,uEAAuE,EACjF,YAAY,WAAW,WAAW,GAC9B,kDACA,2DACJ;0DAED;+CARI;;;;;wCAWX;wCACA,OAAO;oCACT;kDAEA,8OAAC;wCACC,SAAS,IAAM,iBAAiB,WAAW,WAAW,GAAG;wCACzD,UAAU,CAAC,WAAW,WAAW;wCACjC,WAAU;kDAEV,cAAA,8OAAC,+NAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5C;AAEe,SAAS;IACtB,qBACE,8OAAC,8HAAA,CAAA,eAAY;kBACX,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}]}
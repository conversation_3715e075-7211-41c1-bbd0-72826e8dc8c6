import mongoose from 'mongoose';

const FileSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Title is required'],
    trim: true,
    maxlength: [200, 'Title cannot be more than 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true,
    maxlength: [1000, 'Description cannot be more than 1000 characters']
  },
  fileName: {
    type: String,
    required: [true, 'File name is required']
  },
  originalName: {
    type: String,
    required: [true, 'Original file name is required']
  },
  fileType: {
    type: String,
    required: [true, 'File type is required'],
    enum: ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'txt', 'jpg', 'jpeg', 'png', 'gif']
  },
  fileSize: {
    type: Number,
    required: [true, 'File size is required']
  },
  fileUrl: {
    type: String,
    required: [true, 'File URL is required']
  },
  thumbnailUrl: {
    type: String,
    default: null
  },
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Uploader is required']
  },
  subject: {
    type: String,
    required: [true, 'Subject is required'],
    trim: true,
    maxlength: [100, 'Subject cannot be more than 100 characters']
  },
  course: {
    type: String,
    required: [true, 'Course is required'],
    trim: true,
    maxlength: [100, 'Course cannot be more than 100 characters']
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: [true, 'Category is required']
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, 'Tag cannot be more than 30 characters']
  }],
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  isPublic: {
    type: Boolean,
    default: true
  },
  downloadCount: {
    type: Number,
    default: 0
  },
  viewCount: {
    type: Number,
    default: 0
  },
  rating: {
    average: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    count: {
      type: Number,
      default: 0
    }
  },
  moderationNotes: {
    type: String,
    default: ''
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  approvedAt: {
    type: Date,
    default: null
  }
}, {
  timestamps: true
});

// Indexes for better query performance
FileSchema.index({ uploadedBy: 1 });
FileSchema.index({ category: 1 });
FileSchema.index({ subject: 1 });
FileSchema.index({ course: 1 });
FileSchema.index({ status: 1 });
FileSchema.index({ isPublic: 1 });
FileSchema.index({ createdAt: -1 });
FileSchema.index({ downloadCount: -1 });
FileSchema.index({ 'rating.average': -1 });

// Text index for search functionality
FileSchema.index({
  title: 'text',
  description: 'text',
  subject: 'text',
  course: 'text',
  tags: 'text'
});

// Virtual for comments
FileSchema.virtual('comments', {
  ref: 'Comment',
  localField: '_id',
  foreignField: 'file'
});

// Ensure virtual fields are serialized
FileSchema.set('toJSON', { virtuals: true });

export default mongoose.models.File || mongoose.model('File', FileSchema);

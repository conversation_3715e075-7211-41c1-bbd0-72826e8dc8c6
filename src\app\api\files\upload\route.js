import { NextResponse } from 'next/server';
import { requireAuth } from '@/middleware/auth';
import connectDB from '@/lib/mongodb';
import File from '@/models/File';
import Category from '@/models/Category';
import User from '@/models/User';
import imagekit from '@/lib/imagekit';
import { validateFile, generateFileName, getFileType } from '@/lib/fileUtils';

async function handler(request) {
  try {
    const formData = await request.formData();
    const file = formData.get('file');
    const title = formData.get('title');
    const description = formData.get('description');
    const subject = formData.get('subject');
    const course = formData.get('course');
    const categoryId = formData.get('categoryId');
    const tags = formData.get('tags');

    // Validation
    if (!file) {
      return NextResponse.json(
        { success: false, message: 'No file provided' },
        { status: 400 }
      );
    }

    if (!title || !description || !subject || !course || !categoryId) {
      return NextResponse.json(
        { success: false, message: 'Title, description, subject, course, and category are required' },
        { status: 400 }
      );
    }

    // Validate file
    const fileValidation = validateFile(file);
    if (!fileValidation.isValid) {
      return NextResponse.json(
        { success: false, message: fileValidation.errors.join(', ') },
        { status: 400 }
      );
    }

    await connectDB();

    // Check if category exists
    const category = await Category.findById(categoryId);
    if (!category) {
      return NextResponse.json(
        { success: false, message: 'Invalid category' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Generate unique filename
    const fileName = generateFileName(file.name);
    const fileType = getFileType(file.name);

    // Upload to ImageKit
    const uploadResponse = await imagekit.upload({
      file: buffer,
      fileName: fileName,
      folder: '/notex-files',
      useUniqueFileName: false,
    });

    // Parse tags
    const parsedTags = tags ? tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0) : [];

    // Create file record
    const newFile = await File.create({
      title: title.trim(),
      description: description.trim(),
      fileName: fileName,
      originalName: file.name,
      fileType: fileType,
      fileSize: file.size,
      fileUrl: uploadResponse.url,
      thumbnailUrl: uploadResponse.thumbnailUrl || null,
      uploadedBy: request.user._id,
      subject: subject.trim(),
      course: course.trim(),
      category: categoryId,
      tags: parsedTags,
      status: 'pending' // Files need approval by default
    });

    // Update user upload count
    await User.findByIdAndUpdate(request.user._id, {
      $inc: { uploadCount: 1 }
    });

    // Update category file count
    await Category.findByIdAndUpdate(categoryId, {
      $inc: { fileCount: 1 }
    });

    // Populate the response
    await newFile.populate([
      { path: 'uploadedBy', select: 'name email' },
      { path: 'category', select: 'name slug' }
    ]);

    return NextResponse.json({
      success: true,
      message: 'File uploaded successfully and is pending approval',
      data: { file: newFile }
    }, { status: 201 });

  } catch (error) {
    console.error('File upload error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to upload file' },
      { status: 500 }
    );
  }
}

export const POST = requireAuth(handler);
